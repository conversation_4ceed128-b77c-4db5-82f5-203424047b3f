<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>多语言内容管理</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/common.css">
</head>
<body data-user-role="project">
  <div class="layout">
    <!-- 侧边栏 -->
    <div id="sidebar-container"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 头部 -->
      <div id="header-container"></div>

      <!-- 内容区域 -->
      <main class="content">
        <!-- 页面标题 -->
        <div class="page-header">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="page-title">
                <i class="fas fa-file-text"></i>
                多语言内容管理
              </h1>
              <p class="page-description">管理项目中的多语言文本内容和翻译键值</p>
            </div>
            <div class="header-actions">
              <button class="btn btn-secondary" onclick="importContent()">
                <i class="fas fa-upload"></i>
                导入内容
              </button>
              <button class="btn btn-primary" onclick="addNewContent()">
                <i class="fas fa-plus"></i>
                新增内容
              </button>
            </div>
          </div>
        </div>

        <!-- 内容统计 -->
        <div class="stats-grid mb-4">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-list text-primary"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">245</div>
              <div class="stat-label">内容条目总数</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-language text-success"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">8</div>
              <div class="stat-label">支持语言数</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-check-circle text-info"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">1,960</div>
              <div class="stat-label">已翻译条目</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-clock text-warning"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">89.5%</div>
              <div class="stat-label">翻译完成率</div>
            </div>
          </div>
        </div>

        <!-- 内容管理工具栏 -->
        <div class="content-toolbar">
          <div class="toolbar-left">
            <div class="search-section">
              <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" class="form-control" placeholder="搜索内容键或文本..." id="contentSearch">
              </div>
            </div>
            <div class="filter-section">
              <select class="form-control" id="categoryFilter">
                <option value="">全部分类</option>
                <option value="ui">界面文本</option>
                <option value="error">错误信息</option>
                <option value="notification">通知消息</option>
                <option value="help">帮助文档</option>
                <option value="marketing">营销文案</option>
              </select>
            </div>
            <div class="filter-section">
              <select class="form-control" id="statusFilter">
                <option value="">全部状态</option>
                <option value="complete">翻译完成</option>
                <option value="partial">部分翻译</option>
                <option value="untranslated">未翻译</option>
                <option value="outdated">需要更新</option>
              </select>
            </div>
          </div>
          <div class="toolbar-right">
            <button class="btn btn-secondary" onclick="exportContent()">
              <i class="fas fa-download"></i>
              导出
            </button>
            <button class="btn btn-secondary" onclick="batchTranslate()">
              <i class="fas fa-robot"></i>
              批量翻译
            </button>
          </div>
        </div>

        <!-- 内容列表 -->
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">内容列表</h3>
            <div class="card-actions">
              <div class="view-toggle">
                <button class="btn btn-sm btn-secondary active" onclick="switchView('table')">
                  <i class="fas fa-table"></i>
                  表格视图
                </button>
                <button class="btn btn-sm btn-secondary" onclick="switchView('card')">
                  <i class="fas fa-th-large"></i>
                  卡片视图
                </button>
              </div>
            </div>
          </div>
          <div class="card-body">
            <!-- 表格视图 -->
            <div class="content-table-view" id="tableView">
              <div class="table-responsive">
                <table class="table">
                  <thead>
                    <tr>
                      <th><input type="checkbox" class="select-all"></th>
                      <th class="sortable">内容键 <i class="fas fa-sort"></i></th>
                      <th>分类</th>
                      <th>源文本 (中文)</th>
                      <th>翻译进度</th>
                      <th>最后更新</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><input type="checkbox" class="row-select"></td>
                      <td>
                        <code>welcome_message</code>
                        <div class="content-tags">
                          <span class="tag ui">界面</span>
                        </div>
                      </td>
                      <td>界面文本</td>
                      <td class="source-text">欢迎来到我们的电商平台！</td>
                      <td>
                        <div class="progress-indicator">
                          <div class="progress-bar-mini">
                            <div class="progress-fill" style="width: 100%"></div>
                          </div>
                          <span class="progress-text">8/8</span>
                        </div>
                      </td>
                      <td>2024-03-15</td>
                      <td>
                        <div class="action-buttons">
                          <button class="btn btn-sm btn-secondary" onclick="viewContent('welcome_message')">
                            <i class="fas fa-eye"></i>
                          </button>
                          <button class="btn btn-sm btn-primary" onclick="editContent('welcome_message')">
                            <i class="fas fa-edit"></i>
                          </button>
                          <button class="btn btn-sm btn-danger" onclick="deleteContent('welcome_message')">
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td><input type="checkbox" class="row-select"></td>
                      <td>
                        <code>product_not_found</code>
                        <div class="content-tags">
                          <span class="tag error">错误</span>
                        </div>
                      </td>
                      <td>错误信息</td>
                      <td class="source-text">抱歉，找不到您要查看的商品。</td>
                      <td>
                        <div class="progress-indicator">
                          <div class="progress-bar-mini">
                            <div class="progress-fill" style="width: 75%"></div>
                          </div>
                          <span class="progress-text">6/8</span>
                        </div>
                      </td>
                      <td>2024-03-14</td>
                      <td>
                        <div class="action-buttons">
                          <button class="btn btn-sm btn-secondary" onclick="viewContent('product_not_found')">
                            <i class="fas fa-eye"></i>
                          </button>
                          <button class="btn btn-sm btn-primary" onclick="editContent('product_not_found')">
                            <i class="fas fa-edit"></i>
                          </button>
                          <button class="btn btn-sm btn-danger" onclick="deleteContent('product_not_found')">
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td><input type="checkbox" class="row-select"></td>
                      <td>
                        <code>order_success_notification</code>
                        <div class="content-tags">
                          <span class="tag notification">通知</span>
                        </div>
                      </td>
                      <td>通知消息</td>
                      <td class="source-text">您的订单已成功提交，订单号：{orderNumber}</td>
                      <td>
                        <div class="progress-indicator">
                          <div class="progress-bar-mini">
                            <div class="progress-fill" style="width: 50%"></div>
                          </div>
                          <span class="progress-text">4/8</span>
                        </div>
                      </td>
                      <td>2024-03-13</td>
                      <td>
                        <div class="action-buttons">
                          <button class="btn btn-sm btn-secondary" onclick="viewContent('order_success_notification')">
                            <i class="fas fa-eye"></i>
                          </button>
                          <button class="btn btn-sm btn-primary" onclick="editContent('order_success_notification')">
                            <i class="fas fa-edit"></i>
                          </button>
                          <button class="btn btn-sm btn-danger" onclick="deleteContent('order_success_notification')">
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- 卡片视图 -->
            <div class="content-card-view" id="cardView" style="display: none;">
              <div class="content-cards-grid">
                <div class="content-card">
                  <div class="card-header">
                    <div class="card-title">
                      <code>welcome_message</code>
                      <span class="tag ui">界面</span>
                    </div>
                    <div class="card-actions">
                      <button class="btn btn-sm btn-secondary" onclick="viewContent('welcome_message')">
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-primary" onclick="editContent('welcome_message')">
                        <i class="fas fa-edit"></i>
                      </button>
                    </div>
                  </div>
                  <div class="card-content">
                    <div class="source-text">欢迎来到我们的电商平台！</div>
                    <div class="translation-preview">
                      <div class="translation-item">
                        <span class="lang-flag">🇺🇸</span>
                        <span class="translation-text">Welcome to our e-commerce platform!</span>
                      </div>
                      <div class="translation-item">
                        <span class="lang-flag">🇯🇵</span>
                        <span class="translation-text">私たちのeコマースプラットフォームへようこそ！</span>
                      </div>
                    </div>
                  </div>
                  <div class="card-footer">
                    <div class="progress-indicator">
                      <div class="progress-bar-mini">
                        <div class="progress-fill" style="width: 100%"></div>
                      </div>
                      <span class="progress-text">8/8 语言</span>
                    </div>
                    <div class="last-updated">2024-03-15</div>
                  </div>
                </div>

                <div class="content-card">
                  <div class="card-header">
                    <div class="card-title">
                      <code>product_not_found</code>
                      <span class="tag error">错误</span>
                    </div>
                    <div class="card-actions">
                      <button class="btn btn-sm btn-secondary" onclick="viewContent('product_not_found')">
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-primary" onclick="editContent('product_not_found')">
                        <i class="fas fa-edit"></i>
                      </button>
                    </div>
                  </div>
                  <div class="card-content">
                    <div class="source-text">抱歉，找不到您要查看的商品。</div>
                    <div class="translation-preview">
                      <div class="translation-item">
                        <span class="lang-flag">🇺🇸</span>
                        <span class="translation-text">Sorry, the product you're looking for cannot be found.</span>
                      </div>
                      <div class="translation-item missing">
                        <span class="lang-flag">🇯🇵</span>
                        <span class="translation-text">未翻译</span>
                      </div>
                    </div>
                  </div>
                  <div class="card-footer">
                    <div class="progress-indicator">
                      <div class="progress-bar-mini">
                        <div class="progress-fill" style="width: 75%"></div>
                      </div>
                      <span class="progress-text">6/8 语言</span>
                    </div>
                    <div class="last-updated">2024-03-14</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 分页 -->
            <div class="pagination-wrapper">
              <div class="pagination-info">
                显示 1-20 条，共 245 条记录
              </div>
              <div class="pagination">
                <button class="btn btn-sm btn-secondary" disabled>
                  <i class="fas fa-chevron-left"></i>
                </button>
                <button class="btn btn-sm btn-primary">1</button>
                <button class="btn btn-sm btn-secondary">2</button>
                <button class="btn btn-sm btn-secondary">3</button>
                <span class="pagination-dots">...</span>
                <button class="btn btn-sm btn-secondary">13</button>
                <button class="btn btn-sm btn-secondary">
                  <i class="fas fa-chevron-right"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 新增/编辑内容模态框 -->
  <div class="modal-overlay" id="contentModal">
    <div class="modal modal-lg">
      <div class="modal-header">
        <h3 id="modalTitle">新增内容</h3>
        <button class="modal-close" onclick="closeContentModal()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <form id="contentForm">
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">内容键 *</label>
              <input type="text" class="form-control" name="contentKey" required placeholder="例如: welcome_message">
            </div>
            <div class="form-group">
              <label class="form-label">分类 *</label>
              <select class="form-control" name="category" required>
                <option value="">请选择分类</option>
                <option value="ui">界面文本</option>
                <option value="error">错误信息</option>
                <option value="notification">通知消息</option>
                <option value="help">帮助文档</option>
                <option value="marketing">营销文案</option>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="form-label">源文本 (中文) *</label>
            <textarea class="form-control" name="sourceText" rows="3" required placeholder="请输入源文本内容"></textarea>
          </div>
          <div class="form-group">
            <label class="form-label">上下文说明</label>
            <textarea class="form-control" name="context" rows="2" placeholder="描述这段文本的使用场景和上下文"></textarea>
          </div>
          <div class="form-group">
            <label class="form-label">标签</label>
            <input type="text" class="form-control" name="tags" placeholder="用逗号分隔多个标签">
          </div>
          <div class="form-group">
            <label class="form-label">翻译优先级</label>
            <select class="form-control" name="priority">
              <option value="low">低</option>
              <option value="medium" selected>中</option>
              <option value="high">高</option>
              <option value="urgent">紧急</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" onclick="closeContentModal()">取消</button>
        <button class="btn btn-primary" onclick="saveContent()">保存</button>
      </div>
    </div>
  </div>

  <!-- 内容详情模态框 -->
  <div class="modal-overlay" id="viewModal">
    <div class="modal modal-lg">
      <div class="modal-header">
        <h3>内容详情</h3>
        <button class="modal-close" onclick="closeViewModal()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="content-detail">
          <div class="detail-section">
            <h4>基本信息</h4>
            <div class="detail-grid">
              <div class="detail-item">
                <label>内容键:</label>
                <code>welcome_message</code>
              </div>
              <div class="detail-item">
                <label>分类:</label>
                <span>界面文本</span>
              </div>
              <div class="detail-item">
                <label>创建时间:</label>
                <span>2024-03-01 10:30</span>
              </div>
              <div class="detail-item">
                <label>最后更新:</label>
                <span>2024-03-15 14:20</span>
              </div>
            </div>
          </div>

          <div class="detail-section">
            <h4>源文本</h4>
            <div class="source-content">
              <p>欢迎来到我们的电商平台！</p>
            </div>
          </div>

          <div class="detail-section">
            <h4>翻译状态</h4>
            <div class="translations-list">
              <div class="translation-row">
                <div class="translation-lang">
                  <span class="lang-flag">🇺🇸</span>
                  <span class="lang-name">英语</span>
                </div>
                <div class="translation-content">Welcome to our e-commerce platform!</div>
                <div class="translation-status">
                  <span class="status-badge approved">已批准</span>
                </div>
                <div class="translation-actions">
                  <button class="btn btn-sm btn-secondary">编辑</button>
                </div>
              </div>
              <div class="translation-row">
                <div class="translation-lang">
                  <span class="lang-flag">🇯🇵</span>
                  <span class="lang-name">日语</span>
                </div>
                <div class="translation-content">私たちのeコマースプラットフォームへようこそ！</div>
                <div class="translation-status">
                  <span class="status-badge approved">已批准</span>
                </div>
                <div class="translation-actions">
                  <button class="btn btn-sm btn-secondary">编辑</button>
                </div>
              </div>
              <div class="translation-row">
                <div class="translation-lang">
                  <span class="lang-flag">🇰🇷</span>
                  <span class="lang-name">韩语</span>
                </div>
                <div class="translation-content">우리 전자상거래 플랫폼에 오신 것을 환영합니다!</div>
                <div class="translation-status">
                  <span class="status-badge translated">已翻译</span>
                </div>
                <div class="translation-actions">
                  <button class="btn btn-sm btn-secondary">编辑</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" onclick="closeViewModal()">关闭</button>
        <button class="btn btn-primary" onclick="editFromView()">编辑</button>
      </div>
    </div>
  </div>

  <script src="assets/js/common.js"></script>
  <script>
    // 加载组件
    document.addEventListener('DOMContentLoaded', () => {
      fetch('components/sidebar.html')
        .then(response => response.text())
        .then(html => {
          document.getElementById('sidebar-container').innerHTML = html;
        });

      fetch('components/header.html')
        .then(response => response.text())
        .then(html => {
          document.getElementById('header-container').innerHTML = html;
          updateBreadcrumb();
        });
    });

    function updateBreadcrumb() {
      const breadcrumb = document.querySelector('.breadcrumb-current');
      if (breadcrumb) {
        breadcrumb.textContent = '多语言内容管理';
      }
    }

    // 视图切换
    function switchView(viewType) {
      const tableView = document.getElementById('tableView');
      const cardView = document.getElementById('cardView');
      const buttons = document.querySelectorAll('.view-toggle .btn');

      buttons.forEach(btn => btn.classList.remove('active'));
      event.target.classList.add('active');

      if (viewType === 'table') {
        tableView.style.display = 'block';
        cardView.style.display = 'none';
      } else {
        tableView.style.display = 'none';
        cardView.style.display = 'block';
      }
    }

    // 内容操作
    function addNewContent() {
      document.getElementById('modalTitle').textContent = '新增内容';
      document.getElementById('contentForm').reset();
      document.getElementById('contentModal').style.display = 'flex';
    }

    function editContent(key) {
      document.getElementById('modalTitle').textContent = '编辑内容';
      // 这里应该加载现有内容数据
      document.getElementById('contentModal').style.display = 'flex';
    }

    function viewContent(key) {
      document.getElementById('viewModal').style.display = 'flex';
    }

    function deleteContent(key) {
      Utils.confirm('确定要删除这个内容吗？', () => {
        Utils.showNotification(`内容 ${key} 已删除`, 'success');
      });
    }

    function closeContentModal() {
      document.getElementById('contentModal').style.display = 'none';
    }

    function closeViewModal() {
      document.getElementById('viewModal').style.display = 'none';
    }

    function saveContent() {
      const form = document.getElementById('contentForm');
      const formData = new FormData(form);
      
      console.log('保存内容:', Object.fromEntries(formData));
      Utils.showNotification('内容保存成功！', 'success');
      closeContentModal();
    }

    function editFromView() {
      closeViewModal();
      editContent('welcome_message');
    }

    // 批量操作
    function importContent() {
      Utils.showNotification('导入功能开发中...', 'info');
    }

    function exportContent() {
      Utils.showNotification('正在导出内容...', 'info');
    }

    function batchTranslate() {
      const selected = document.querySelectorAll('.row-select:checked');
      if (selected.length > 0) {
        Utils.confirm(`确定要对 ${selected.length} 个内容进行批量翻译吗？`, () => {
          Utils.showNotification('批量翻译已启动', 'success');
        });
      } else {
        Utils.showNotification('请先选择要翻译的内容', 'warning');
      }
    }

    // 全选功能
    document.addEventListener('change', (e) => {
      if (e.target.classList.contains('select-all')) {
        const checkboxes = document.querySelectorAll('.row-select');
        checkboxes.forEach(cb => cb.checked = e.target.checked);
      }
    });
  </script>
</body>
</html>
