<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>多语言管理后台 - 首页</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/common.css">
</head>
<body data-user-role="admin">
  <div class="layout">
    <!-- 侧边栏 -->
    <div id="sidebar-container"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 头部 -->
      <div id="header-container"></div>

      <!-- 内容区域 -->
      <main class="content">
        <!-- 欢迎区域 -->
        <div class="welcome-section">
          <div class="welcome-content">
            <h1 class="welcome-title">
              <i class="fas fa-globe"></i>
              多语言管理后台
            </h1>
            <p class="welcome-description">
              专业的多语言内容管理平台，支持翻译工作流、质量控制、语言包管理等完整功能
            </p>
          </div>
          <div class="welcome-image">
            <img src="https://images.unsplash.com/photo-1557804506-669a67965ba0?w=400&h=300&fit=crop" 
                 alt="多语言管理" class="feature-image">
          </div>
        </div>

        <!-- 功能模块导航 -->
        <div class="modules-section">
          <h2 class="section-title">核心功能模块</h2>
          <div class="modules-grid">
            <!-- 翻译工作台 -->
            <div class="module-card">
              <div class="module-icon">
                <i class="fas fa-language"></i>
              </div>
              <div class="module-content">
                <h3 class="module-title">翻译工作台</h3>
                <p class="module-description">
                  在线翻译界面，支持实时翻译、术语管理、翻译记忆等专业功能
                </p>
                <div class="module-features">
                  <span class="feature-tag">实时翻译</span>
                  <span class="feature-tag">术语管理</span>
                  <span class="feature-tag">翻译记忆</span>
                </div>
              </div>
              <div class="module-actions">
                <a href="translation-workbench.html" class="btn btn-primary">
                  <i class="fas fa-arrow-right"></i>
                  进入工作台
                </a>
              </div>
            </div>

            <!-- 语言包管理 -->
            <div class="module-card">
              <div class="module-icon">
                <i class="fas fa-box"></i>
              </div>
              <div class="module-content">
                <h3 class="module-title">语言包管理</h3>
                <p class="module-description">
                  管理多语言文件的导入导出、版本控制、批量操作等功能
                </p>
                <div class="module-features">
                  <span class="feature-tag">版本管理</span>
                  <span class="feature-tag">批量操作</span>
                  <span class="feature-tag">导入导出</span>
                </div>
              </div>
              <div class="module-actions">
                <a href="language-pack-manager.html" class="btn btn-primary">
                  <i class="fas fa-arrow-right"></i>
                  管理语言包
                </a>
              </div>
            </div>

            <!-- 翻译质量控制 -->
            <div class="module-card">
              <div class="module-icon">
                <i class="fas fa-check-circle"></i>
              </div>
              <div class="module-content">
                <h3 class="module-title">翻译质量控制</h3>
                <p class="module-description">
                  翻译审核、质量评估、错误检查等质量保证功能
                </p>
                <div class="module-features">
                  <span class="feature-tag">翻译审核</span>
                  <span class="feature-tag">质量评估</span>
                  <span class="feature-tag">错误检查</span>
                </div>
              </div>
              <div class="module-actions">
                <a href="translation-quality-control.html" class="btn btn-primary">
                  <i class="fas fa-arrow-right"></i>
                  质量控制
                </a>
              </div>
            </div>

            <!-- 多语言内容管理 -->
            <div class="module-card">
              <div class="module-icon">
                <i class="fas fa-file-text"></i>
              </div>
              <div class="module-content">
                <h3 class="module-title">多语言内容管理</h3>
                <p class="module-description">
                  管理多语言文本内容的增删改查、批量管理等功能
                </p>
                <div class="module-features">
                  <span class="feature-tag">内容管理</span>
                  <span class="feature-tag">批量编辑</span>
                  <span class="feature-tag">搜索筛选</span>
                </div>
              </div>
              <div class="module-actions">
                <a href="multilingual-content-manager.html" class="btn btn-primary">
                  <i class="fas fa-arrow-right"></i>
                  管理内容
                </a>
              </div>
            </div>

            <!-- 全局语言配置 -->
            <div class="module-card">
              <div class="module-icon">
                <i class="fas fa-cog"></i>
              </div>
              <div class="module-content">
                <h3 class="module-title">全局语言配置</h3>
                <p class="module-description">
                  管理系统支持的语言、翻译规则和全局配置
                </p>
                <div class="module-features">
                  <span class="feature-tag">语言管理</span>
                  <span class="feature-tag">翻译规则</span>
                  <span class="feature-tag">全局配置</span>
                </div>
              </div>
              <div class="module-actions">
                <a href="admin-languages.html" class="btn btn-primary">
                  <i class="fas fa-arrow-right"></i>
                  语言配置
                </a>
              </div>
            </div>

            <!-- 运营仪表板 -->
            <div class="module-card">
              <div class="module-icon">
                <i class="fas fa-tachometer-alt"></i>
              </div>
              <div class="module-content">
                <h3 class="module-title">运营仪表板</h3>
                <p class="module-description">
                  系统总览、关键指标监控和数据分析
                </p>
                <div class="module-features">
                  <span class="feature-tag">数据统计</span>
                  <span class="feature-tag">趋势分析</span>
                  <span class="feature-tag">实时监控</span>
                </div>
              </div>
              <div class="module-actions">
                <a href="admin-dashboard.html" class="btn btn-primary">
                  <i class="fas fa-arrow-right"></i>
                  查看仪表板
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速开始 -->
        <div class="quick-start-section">
          <h2 class="section-title">快速开始</h2>
          <div class="quick-start-grid">
            <div class="quick-start-item">
              <div class="step-number">1</div>
              <div class="step-content">
                <h4>配置支持语言</h4>
                <p>在全局语言配置中添加项目需要支持的语言</p>
                <a href="admin-languages.html" class="step-link">配置语言 <i class="fas fa-arrow-right"></i></a>
              </div>
            </div>
            <div class="quick-start-item">
              <div class="step-number">2</div>
              <div class="step-content">
                <h4>添加翻译内容</h4>
                <p>在内容管理中添加需要翻译的文本内容</p>
                <a href="multilingual-content-manager.html" class="step-link">管理内容 <i class="fas fa-arrow-right"></i></a>
              </div>
            </div>
            <div class="quick-start-item">
              <div class="step-number">3</div>
              <div class="step-content">
                <h4>开始翻译工作</h4>
                <p>使用翻译工作台进行专业的翻译工作</p>
                <a href="translation-workbench.html" class="step-link">开始翻译 <i class="fas fa-arrow-right"></i></a>
              </div>
            </div>
            <div class="quick-start-item">
              <div class="step-number">4</div>
              <div class="step-content">
                <h4>质量控制审核</h4>
                <p>通过质量控制系统审核和优化翻译质量</p>
                <a href="translation-quality-control.html" class="step-link">质量审核 <i class="fas fa-arrow-right"></i></a>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统特性 -->
        <div class="features-section">
          <h2 class="section-title">系统特性</h2>
          <div class="features-grid">
            <div class="feature-item">
              <div class="feature-icon">
                <i class="fas fa-rocket"></i>
              </div>
              <h4>高效翻译</h4>
              <p>AI辅助翻译、翻译记忆、术语管理等功能提升翻译效率</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <i class="fas fa-shield-alt"></i>
              </div>
              <h4>质量保证</h4>
              <p>多层次质量控制体系，确保翻译质量和一致性</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <i class="fas fa-users"></i>
              </div>
              <h4>团队协作</h4>
              <p>支持多角色协作，翻译、审核、管理分工明确</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <i class="fas fa-chart-line"></i>
              </div>
              <h4>数据分析</h4>
              <p>详细的翻译进度统计和质量分析报告</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <script src="assets/js/common.js"></script>
  <script>
    // 加载组件
    document.addEventListener('DOMContentLoaded', () => {
      fetch('components/sidebar.html')
        .then(response => response.text())
        .then(html => {
          document.getElementById('sidebar-container').innerHTML = html;
        });

      fetch('components/header.html')
        .then(response => response.text())
        .then(html => {
          document.getElementById('header-container').innerHTML = html;
          updateBreadcrumb();
        });
    });

    function updateBreadcrumb() {
      const breadcrumb = document.querySelector('.breadcrumb-current');
      if (breadcrumb) {
        breadcrumb.textContent = '首页';
      }
    }
  </script>
</body>
</html>
