<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>运营后台 - 全局语言配置</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/common.css">
</head>
<body data-user-role="admin">
  <div class="layout">
    <!-- 侧边栏 -->
    <div id="sidebar-container"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 头部 -->
      <div id="header-container"></div>

      <!-- 内容区域 -->
      <main class="content">
        <!-- 页面标题 -->
        <div class="page-header">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="page-title">
                <i class="fas fa-language"></i>
                全局语言配置
              </h1>
              <p class="page-description">管理系统支持的语言和翻译规则</p>
            </div>
            <button class="btn btn-primary" onclick="openAddLanguageModal()">
              <i class="fas fa-plus"></i>
              添加语言
            </button>
          </div>
        </div>

        <!-- 语言统计 -->
        <div class="stats-grid mb-4">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-globe text-primary"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">45</div>
              <div class="stat-label">支持语言总数</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-check-circle text-success"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">42</div>
              <div class="stat-label">已启用语言</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-clock text-warning"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">3</div>
              <div class="stat-label">待配置语言</div>
            </div>
          </div>
        </div>

        <!-- 语言列表 -->
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">语言列表</h3>
            <div class="card-actions">
              <button class="btn btn-sm btn-secondary">
                <i class="fas fa-download"></i>
                导出配置
              </button>
              <button class="btn btn-sm btn-secondary">
                <i class="fas fa-upload"></i>
                导入配置
              </button>
            </div>
          </div>
          <div class="card-body">
            <!-- 搜索和筛选 -->
            <div class="filter-row mb-3">
              <div class="filter-group">
                <div class="search-box">
                  <i class="fas fa-search"></i>
                  <input type="text" class="form-control table-filter" placeholder="搜索语言名称或代码...">
                </div>
              </div>
              <div class="filter-group">
                <select class="form-control">
                  <option value="">全部状态</option>
                  <option value="enabled">已启用</option>
                  <option value="disabled">已禁用</option>
                  <option value="pending">待配置</option>
                </select>
              </div>
              <div class="filter-group">
                <select class="form-control">
                  <option value="">全部地区</option>
                  <option value="asia">亚洲</option>
                  <option value="europe">欧洲</option>
                  <option value="america">美洲</option>
                  <option value="africa">非洲</option>
                </select>
              </div>
            </div>

            <div class="table-responsive">
              <table class="table">
                <thead>
                  <tr>
                    <th>语言</th>
                    <th>语言代码</th>
                    <th>地区</th>
                    <th>使用项目数</th>
                    <th>翻译质量</th>
                    <th>状态</th>
                    <th>最后更新</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>
                      <div class="language-info">
                        <span class="language-flag">🇨🇳</span>
                        <div>
                          <div class="language-name">简体中文</div>
                          <div class="language-native">中文（简体）</div>
                        </div>
                      </div>
                    </td>
                    <td><code>zh-CN</code></td>
                    <td>中国大陆</td>
                    <td><span class="badge badge-info">1,245</span></td>
                    <td>
                      <div class="quality-indicator">
                        <div class="quality-bar">
                          <div class="quality-fill" style="width: 95%"></div>
                        </div>
                        <span class="quality-text">95%</span>
                      </div>
                    </td>
                    <td><span class="status-badge active">已启用</span></td>
                    <td>2024-03-15</td>
                    <td>
                      <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary" onclick="configLanguage('zh-CN')">
                          <i class="fas fa-cog"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editLanguage('zh-CN')">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="toggleLanguage('zh-CN')">
                          <i class="fas fa-pause"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <div class="language-info">
                        <span class="language-flag">🇺🇸</span>
                        <div>
                          <div class="language-name">英语</div>
                          <div class="language-native">English</div>
                        </div>
                      </div>
                    </td>
                    <td><code>en-US</code></td>
                    <td>美国</td>
                    <td><span class="badge badge-info">2,156</span></td>
                    <td>
                      <div class="quality-indicator">
                        <div class="quality-bar">
                          <div class="quality-fill" style="width: 98%"></div>
                        </div>
                        <span class="quality-text">98%</span>
                      </div>
                    </td>
                    <td><span class="status-badge active">已启用</span></td>
                    <td>2024-03-14</td>
                    <td>
                      <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary" onclick="configLanguage('en-US')">
                          <i class="fas fa-cog"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editLanguage('en-US')">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="toggleLanguage('en-US')">
                          <i class="fas fa-pause"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <div class="language-info">
                        <span class="language-flag">🇯🇵</span>
                        <div>
                          <div class="language-name">日语</div>
                          <div class="language-native">日本語</div>
                        </div>
                      </div>
                    </td>
                    <td><code>ja-JP</code></td>
                    <td>日本</td>
                    <td><span class="badge badge-info">892</span></td>
                    <td>
                      <div class="quality-indicator">
                        <div class="quality-bar">
                          <div class="quality-fill" style="width: 87%"></div>
                        </div>
                        <span class="quality-text">87%</span>
                      </div>
                    </td>
                    <td><span class="status-badge active">已启用</span></td>
                    <td>2024-03-13</td>
                    <td>
                      <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary" onclick="configLanguage('ja-JP')">
                          <i class="fas fa-cog"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editLanguage('ja-JP')">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="toggleLanguage('ja-JP')">
                          <i class="fas fa-pause"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <div class="language-info">
                        <span class="language-flag">🇰🇷</span>
                        <div>
                          <div class="language-name">韩语</div>
                          <div class="language-native">한국어</div>
                        </div>
                      </div>
                    </td>
                    <td><code>ko-KR</code></td>
                    <td>韩国</td>
                    <td><span class="badge badge-info">567</span></td>
                    <td>
                      <div class="quality-indicator">
                        <div class="quality-bar">
                          <div class="quality-fill" style="width: 82%"></div>
                        </div>
                        <span class="quality-text">82%</span>
                      </div>
                    </td>
                    <td><span class="status-badge inactive">待配置</span></td>
                    <td>2024-03-10</td>
                    <td>
                      <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary" onclick="configLanguage('ko-KR')">
                          <i class="fas fa-cog"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editLanguage('ko-KR')">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="toggleLanguage('ko-KR')">
                          <i class="fas fa-play"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- 翻译规则配置 -->
        <div class="card mt-4">
          <div class="card-header">
            <h3 class="card-title">翻译规则配置</h3>
          </div>
          <div class="card-body">
            <div class="rules-grid">
              <div class="rule-item">
                <div class="rule-header">
                  <h4>自动翻译</h4>
                  <label class="switch">
                    <input type="checkbox" checked>
                    <span class="slider"></span>
                  </label>
                </div>
                <p class="rule-description">启用AI自动翻译功能，提高翻译效率</p>
              </div>
              <div class="rule-item">
                <div class="rule-header">
                  <h4>质量检查</h4>
                  <label class="switch">
                    <input type="checkbox" checked>
                    <span class="slider"></span>
                  </label>
                </div>
                <p class="rule-description">自动检查翻译质量，标记可能的问题</p>
              </div>
              <div class="rule-item">
                <div class="rule-header">
                  <h4>术语一致性</h4>
                  <label class="switch">
                    <input type="checkbox" checked>
                    <span class="slider"></span>
                  </label>
                </div>
                <p class="rule-description">确保专业术语在不同项目中保持一致</p>
              </div>
              <div class="rule-item">
                <div class="rule-header">
                  <h4>版本控制</h4>
                  <label class="switch">
                    <input type="checkbox">
                    <span class="slider"></span>
                  </label>
                </div>
                <p class="rule-description">启用翻译版本控制，跟踪变更历史</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 添加语言模态框 -->
  <div class="modal-overlay" id="addLanguageModal">
    <div class="modal">
      <div class="modal-header">
        <h3>添加新语言</h3>
        <button class="modal-close" onclick="closeAddLanguageModal()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <form id="addLanguageForm">
          <div class="form-group">
            <label class="form-label">语言名称 *</label>
            <input type="text" class="form-control" name="languageName" required>
          </div>
          <div class="form-group">
            <label class="form-label">本地化名称 *</label>
            <input type="text" class="form-control" name="nativeName" required>
          </div>
          <div class="form-group">
            <label class="form-label">语言代码 *</label>
            <input type="text" class="form-control" name="languageCode" required placeholder="例如: zh-CN">
          </div>
          <div class="form-group">
            <label class="form-label">地区</label>
            <input type="text" class="form-control" name="region">
          </div>
          <div class="form-group">
            <label class="form-label">文字方向</label>
            <select class="form-control" name="direction">
              <option value="ltr">从左到右 (LTR)</option>
              <option value="rtl">从右到左 (RTL)</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">状态</label>
            <select class="form-control" name="status">
              <option value="enabled">启用</option>
              <option value="disabled">禁用</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" onclick="closeAddLanguageModal()">取消</button>
        <button class="btn btn-primary" onclick="addLanguage()">添加</button>
      </div>
    </div>
  </div>

  <script src="assets/js/common.js"></script>
  <script>
    // 加载组件
    document.addEventListener('DOMContentLoaded', () => {
      fetch('components/sidebar.html')
        .then(response => response.text())
        .then(html => {
          document.getElementById('sidebar-container').innerHTML = html;
        });

      fetch('components/header.html')
        .then(response => response.text())
        .then(html => {
          document.getElementById('header-container').innerHTML = html;
          updateBreadcrumb();
        });
    });

    function updateBreadcrumb() {
      const breadcrumb = document.querySelector('.breadcrumb-current');
      if (breadcrumb) {
        breadcrumb.textContent = '全局语言配置';
      }
    }

    // 模态框操作
    function openAddLanguageModal() {
      document.getElementById('addLanguageModal').style.display = 'flex';
    }

    function closeAddLanguageModal() {
      document.getElementById('addLanguageModal').style.display = 'none';
      document.getElementById('addLanguageForm').reset();
    }

    function addLanguage() {
      const form = document.getElementById('addLanguageForm');
      const formData = new FormData(form);
      
      console.log('添加语言:', Object.fromEntries(formData));
      Utils.showNotification('语言添加成功！', 'success');
      closeAddLanguageModal();
    }

    // 语言操作
    function configLanguage(code) {
      console.log('配置语言:', code);
    }

    function editLanguage(code) {
      console.log('编辑语言:', code);
    }

    function toggleLanguage(code) {
      console.log('切换语言状态:', code);
      Utils.showNotification('语言状态已更新！', 'success');
    }
  </script>
</body>
</html>
