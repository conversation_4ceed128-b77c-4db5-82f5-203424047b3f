<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>简易多语言管理后台</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
      color: #333;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 20px;
    }

    .header h1 {
      color: #2563eb;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .tabs {
      display: flex;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 20px;
    }

    .tab {
      flex: 1;
      padding: 15px 20px;
      background: #f8f9fa;
      border: none;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .tab.active {
      background: #2563eb;
      color: white;
    }

    .tab:hover:not(.active) {
      background: #e5e7eb;
    }

    .content {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      padding: 20px;
      min-height: 500px;
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    .btn-primary {
      background: #2563eb;
      color: white;
    }

    .btn-primary:hover {
      background: #1d4ed8;
    }

    .btn-success {
      background: #059669;
      color: white;
    }

    .btn-danger {
      background: #dc2626;
      color: white;
    }

    .btn-secondary {
      background: #6b7280;
      color: white;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
    }

    .form-control {
      width: 100%;
      padding: 10px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 14px;
    }

    .form-control:focus {
      outline: none;
      border-color: #2563eb;
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }

    .language-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .language-card {
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 20px;
      text-align: center;
      transition: all 0.3s;
    }

    .language-card:hover {
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      border-color: #2563eb;
    }

    .language-flag {
      font-size: 32px;
      margin-bottom: 10px;
    }

    .language-name {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 5px;
    }

    .language-code {
      color: #6b7280;
      font-size: 14px;
      margin-bottom: 15px;
    }

    .translation-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .translation-table th,
    .translation-table td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #e5e7eb;
    }

    .translation-table th {
      background: #f8f9fa;
      font-weight: 600;
    }

    .translation-input {
      width: 100%;
      padding: 8px;
      border: 1px solid #d1d5db;
      border-radius: 4px;
      font-size: 14px;
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 1000;
    }

    .modal-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      padding: 30px;
      border-radius: 8px;
      width: 90%;
      max-width: 500px;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .modal-close {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #6b7280;
    }

    .language-selector {
      margin-bottom: 20px;
    }

    .language-selector select {
      padding: 10px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 14px;
    }

    .content-item {
      display: flex;
      gap: 15px;
      align-items: center;
      padding: 15px;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      margin-bottom: 10px;
    }

    .content-key {
      min-width: 150px;
      font-weight: 500;
      color: #374151;
    }

    .content-translation {
      flex: 1;
    }

    .add-content-form {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 6px;
      margin-bottom: 20px;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr auto;
      gap: 15px;
      align-items: end;
    }

    .success-message {
      background: #d1fae5;
      color: #065f46;
      padding: 10px 15px;
      border-radius: 6px;
      margin-bottom: 15px;
      display: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 头部 -->
    <div class="header">
      <h1>
        <i class="fas fa-globe"></i>
        简易多语言管理后台
      </h1>
      <p>管理系统支持的语言、菜单翻译和自定义内容翻译</p>
    </div>

    <!-- 标签页导航 -->
    <div class="tabs">
      <button class="tab active" onclick="switchTab('languages')">
        <i class="fas fa-language"></i>
        语言管理
      </button>
      <button class="tab" onclick="switchTab('menus')">
        <i class="fas fa-bars"></i>
        菜单翻译
      </button>
      <button class="tab" onclick="switchTab('content')">
        <i class="fas fa-file-text"></i>
        内容翻译
      </button>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 语言管理 -->
      <div id="languages" class="tab-content active">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <h2>语言管理</h2>
          <button class="btn btn-primary" onclick="openAddLanguageModal()">
            <i class="fas fa-plus"></i>
            新建语言
          </button>
        </div>

        <div class="language-grid" id="languageGrid">
          <!-- 语言卡片将通过JavaScript动态生成 -->
        </div>
      </div>

      <!-- 菜单翻译 -->
      <div id="menus" class="tab-content">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <h2>菜单翻译</h2>
          <div class="language-selector">
            <label>选择语言：</label>
            <select id="menuLanguageSelect" onchange="loadMenuTranslations()">
              <option value="">请选择语言</option>
            </select>
          </div>
        </div>

        <div class="success-message" id="menuSuccessMessage">
          菜单翻译保存成功！
        </div>

        <table class="translation-table" id="menuTable">
          <thead>
            <tr>
              <th>菜单项</th>
              <th>中文原文</th>
              <th>翻译</th>
            </tr>
          </thead>
          <tbody id="menuTableBody">
            <!-- 菜单翻译项将通过JavaScript动态生成 -->
          </tbody>
        </table>

        <div style="margin-top: 20px;">
          <button class="btn btn-success" onclick="saveMenuTranslations()">
            <i class="fas fa-save"></i>
            保存菜单翻译
          </button>
        </div>
      </div>

      <!-- 内容翻译 -->
      <div id="content" class="tab-content">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <h2>内容翻译</h2>
          <div class="language-selector">
            <label>选择语言：</label>
            <select id="contentLanguageSelect" onchange="loadContentTranslations()">
              <option value="">请选择语言</option>
            </select>
          </div>
        </div>

        <!-- 添加新内容 -->
        <div class="add-content-form">
          <h3>添加新内容</h3>
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">内容标识</label>
              <input type="text" class="form-control" id="newContentKey" placeholder="例如: welcome_message">
            </div>
            <div class="form-group">
              <label class="form-label">中文内容</label>
              <input type="text" class="form-control" id="newContentText" placeholder="例如: 欢迎使用我们的系统">
            </div>
            <div>
              <button class="btn btn-primary" onclick="addNewContent()">
                <i class="fas fa-plus"></i>
                添加
              </button>
            </div>
          </div>
        </div>

        <div class="success-message" id="contentSuccessMessage">
          内容翻译保存成功！
        </div>

        <div id="contentList">
          <!-- 内容翻译项将通过JavaScript动态生成 -->
        </div>

        <div style="margin-top: 20px;">
          <button class="btn btn-success" onclick="saveContentTranslations()">
            <i class="fas fa-save"></i>
            保存内容翻译
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 新建语言模态框 -->
  <div id="addLanguageModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>新建语言</h3>
        <button class="modal-close" onclick="closeAddLanguageModal()">&times;</button>
      </div>
      <form id="addLanguageForm">
        <div class="form-group">
          <label class="form-label">语言名称</label>
          <input type="text" class="form-control" id="languageName" placeholder="例如: 英语" required>
        </div>
        <div class="form-group">
          <label class="form-label">语言代码</label>
          <input type="text" class="form-control" id="languageCode" placeholder="例如: en-US" required>
        </div>
        <div class="form-group">
          <label class="form-label">语言图标</label>
          <input type="text" class="form-control" id="languageFlag" placeholder="例如: 🇺🇸" required>
        </div>
        <div style="display: flex; gap: 10px; justify-content: flex-end;">
          <button type="button" class="btn btn-secondary" onclick="closeAddLanguageModal()">取消</button>
          <button type="submit" class="btn btn-primary">创建语言</button>
        </div>
      </form>
    </div>
  </div>

  <script>
    // 数据存储
    let languages = [
      { name: '简体中文', code: 'zh-CN', flag: '🇨🇳', isDefault: true },
      { name: '英语', code: 'en-US', flag: '🇺🇸' },
      { name: '日语', code: 'ja-JP', flag: '🇯🇵' }
    ];

    // 固定菜单项
    const menuItems = [
      { key: 'dashboard', text: '仪表板' },
      { key: 'users', text: '用户管理' },
      { key: 'products', text: '产品管理' },
      { key: 'orders', text: '订单管理' },
      { key: 'settings', text: '系统设置' },
      { key: 'reports', text: '报表分析' },
      { key: 'help', text: '帮助中心' },
      { key: 'logout', text: '退出登录' }
    ];

    // 翻译数据
    let menuTranslations = {};
    let contentTranslations = {};
    let customContent = [
      { key: 'welcome_message', text: '欢迎使用我们的系统' },
      { key: 'success_message', text: '操作成功完成' },
      { key: 'error_message', text: '操作失败，请重试' }
    ];

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
      renderLanguages();
      updateLanguageSelectors();
      loadMenuTranslations();
      loadContentTranslations();
    });

    // 标签页切换
    function switchTab(tabName) {
      // 更新标签页状态
      document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
      document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
      
      event.target.classList.add('active');
      document.getElementById(tabName).classList.add('active');
    }

    // 渲染语言列表
    function renderLanguages() {
      const grid = document.getElementById('languageGrid');
      grid.innerHTML = languages.map(lang => `
        <div class="language-card">
          <div class="language-flag">${lang.flag}</div>
          <div class="language-name">${lang.name}</div>
          <div class="language-code">${lang.code}</div>
          <div style="display: flex; gap: 10px; justify-content: center;">
            ${!lang.isDefault ? `<button class="btn btn-danger" onclick="deleteLanguage('${lang.code}')">
              <i class="fas fa-trash"></i>
              删除
            </button>` : '<span style="color: #6b7280;">默认语言</span>'}
          </div>
        </div>
      `).join('');
    }

    // 更新语言选择器
    function updateLanguageSelectors() {
      const menuSelect = document.getElementById('menuLanguageSelect');
      const contentSelect = document.getElementById('contentLanguageSelect');
      
      const options = languages.filter(lang => !lang.isDefault).map(lang => 
        `<option value="${lang.code}">${lang.flag} ${lang.name}</option>`
      ).join('');
      
      menuSelect.innerHTML = '<option value="">请选择语言</option>' + options;
      contentSelect.innerHTML = '<option value="">请选择语言</option>' + options;
    }

    // 新建语言
    function openAddLanguageModal() {
      document.getElementById('addLanguageModal').style.display = 'block';
    }

    function closeAddLanguageModal() {
      document.getElementById('addLanguageModal').style.display = 'none';
      document.getElementById('addLanguageForm').reset();
    }

    document.getElementById('addLanguageForm').addEventListener('submit', function(e) {
      e.preventDefault();
      
      const name = document.getElementById('languageName').value;
      const code = document.getElementById('languageCode').value;
      const flag = document.getElementById('languageFlag').value;
      
      // 检查语言代码是否已存在
      if (languages.find(lang => lang.code === code)) {
        alert('该语言代码已存在！');
        return;
      }
      
      languages.push({ name, code, flag });
      renderLanguages();
      updateLanguageSelectors();
      closeAddLanguageModal();
      
      alert('语言创建成功！');
    });

    // 删除语言
    function deleteLanguage(code) {
      if (confirm('确定要删除这个语言吗？相关的翻译数据也会被删除。')) {
        languages = languages.filter(lang => lang.code !== code);
        delete menuTranslations[code];
        delete contentTranslations[code];
        renderLanguages();
        updateLanguageSelectors();
        alert('语言删除成功！');
      }
    }

    // 加载菜单翻译
    function loadMenuTranslations() {
      const selectedLang = document.getElementById('menuLanguageSelect').value;
      const tbody = document.getElementById('menuTableBody');
      
      if (!selectedLang) {
        tbody.innerHTML = '<tr><td colspan="3" style="text-align: center; color: #6b7280;">请先选择要翻译的语言</td></tr>';
        return;
      }
      
      tbody.innerHTML = menuItems.map(item => `
        <tr>
          <td>${item.key}</td>
          <td>${item.text}</td>
          <td>
            <input type="text" class="translation-input" 
                   value="${menuTranslations[selectedLang]?.[item.key] || ''}"
                   onchange="updateMenuTranslation('${selectedLang}', '${item.key}', this.value)">
          </td>
        </tr>
      `).join('');
    }

    // 更新菜单翻译
    function updateMenuTranslation(langCode, key, value) {
      if (!menuTranslations[langCode]) {
        menuTranslations[langCode] = {};
      }
      menuTranslations[langCode][key] = value;
    }

    // 保存菜单翻译
    function saveMenuTranslations() {
      const message = document.getElementById('menuSuccessMessage');
      message.style.display = 'block';
      setTimeout(() => {
        message.style.display = 'none';
      }, 3000);
      
      console.log('菜单翻译数据:', menuTranslations);
    }

    // 加载内容翻译
    function loadContentTranslations() {
      const selectedLang = document.getElementById('contentLanguageSelect').value;
      const contentList = document.getElementById('contentList');
      
      if (!selectedLang) {
        contentList.innerHTML = '<p style="text-align: center; color: #6b7280; padding: 40px;">请先选择要翻译的语言</p>';
        return;
      }
      
      contentList.innerHTML = customContent.map(item => `
        <div class="content-item">
          <div class="content-key">${item.key}</div>
          <div style="flex: 1;">
            <div style="margin-bottom: 5px; font-size: 14px; color: #6b7280;">中文: ${item.text}</div>
            <input type="text" class="form-control content-translation" 
                   placeholder="请输入翻译内容"
                   value="${contentTranslations[selectedLang]?.[item.key] || ''}"
                   onchange="updateContentTranslation('${selectedLang}', '${item.key}', this.value)">
          </div>
          <button class="btn btn-danger" onclick="deleteContent('${item.key}')">
            <i class="fas fa-trash"></i>
          </button>
        </div>
      `).join('');
    }

    // 更新内容翻译
    function updateContentTranslation(langCode, key, value) {
      if (!contentTranslations[langCode]) {
        contentTranslations[langCode] = {};
      }
      contentTranslations[langCode][key] = value;
    }

    // 添加新内容
    function addNewContent() {
      const key = document.getElementById('newContentKey').value.trim();
      const text = document.getElementById('newContentText').value.trim();
      
      if (!key || !text) {
        alert('请填写完整的内容标识和中文内容');
        return;
      }
      
      if (customContent.find(item => item.key === key)) {
        alert('该内容标识已存在！');
        return;
      }
      
      customContent.push({ key, text });
      document.getElementById('newContentKey').value = '';
      document.getElementById('newContentText').value = '';
      
      // 重新加载当前语言的内容翻译
      loadContentTranslations();
      
      alert('内容添加成功！');
    }

    // 删除内容
    function deleteContent(key) {
      if (confirm('确定要删除这个内容吗？')) {
        customContent = customContent.filter(item => item.key !== key);
        
        // 删除所有语言中的相关翻译
        Object.keys(contentTranslations).forEach(langCode => {
          delete contentTranslations[langCode][key];
        });
        
        loadContentTranslations();
        alert('内容删除成功！');
      }
    }

    // 保存内容翻译
    function saveContentTranslations() {
      const message = document.getElementById('contentSuccessMessage');
      message.style.display = 'block';
      setTimeout(() => {
        message.style.display = 'none';
      }, 3000);
      
      console.log('内容翻译数据:', contentTranslations);
    }

    // 点击模态框外部关闭
    window.onclick = function(event) {
      const modal = document.getElementById('addLanguageModal');
      if (event.target === modal) {
        closeAddLanguageModal();
      }
    }
  </script>
</body>
</html>
