<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>简易多语言管理后台</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
      color: #333;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 20px;
    }

    .header h1 {
      color: #2563eb;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .tabs {
      display: flex;
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 20px;
    }

    .tab {
      flex: 1;
      padding: 15px 20px;
      background: #f8f9fa;
      border: none;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .tab.active {
      background: #2563eb;
      color: white;
    }

    .tab:hover:not(.active) {
      background: #e5e7eb;
    }

    .content {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      padding: 20px;
      min-height: 500px;
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    .btn-primary {
      background: #2563eb;
      color: white;
    }

    .btn-primary:hover {
      background: #1d4ed8;
    }

    .btn-success {
      background: #059669;
      color: white;
    }

    .btn-danger {
      background: #dc2626;
      color: white;
    }

    .btn-secondary {
      background: #6b7280;
      color: white;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
    }

    .form-control {
      width: 100%;
      padding: 10px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 14px;
    }

    .form-control:focus {
      outline: none;
      border-color: #2563eb;
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }

    .language-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .language-card {
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 20px;
      text-align: center;
      transition: all 0.3s;
    }

    .language-card:hover {
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      border-color: #2563eb;
    }

    .language-flag {
      font-size: 32px;
      margin-bottom: 10px;
    }

    .language-name {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 5px;
    }

    .language-code {
      color: #6b7280;
      font-size: 14px;
      margin-bottom: 15px;
    }

    .translation-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .translation-table th,
    .translation-table td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #e5e7eb;
    }

    .translation-table th {
      background: #f8f9fa;
      font-weight: 600;
    }

    .translation-input {
      width: 100%;
      padding: 8px;
      border: 1px solid #d1d5db;
      border-radius: 4px;
      font-size: 14px;
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 1000;
    }

    .modal-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      padding: 30px;
      border-radius: 8px;
      width: 90%;
      max-width: 500px;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .modal-close {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #6b7280;
    }

    .language-selector {
      margin-bottom: 20px;
    }

    .language-selector select {
      padding: 10px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 14px;
    }

    .content-item {
      display: flex;
      gap: 15px;
      align-items: center;
      padding: 15px;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      margin-bottom: 10px;
    }

    .content-key {
      min-width: 150px;
      font-weight: 500;
      color: #374151;
    }

    .content-translation {
      flex: 1;
    }

    .add-content-form {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 6px;
      margin-bottom: 20px;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr auto;
      gap: 15px;
      align-items: end;
    }

    .success-message {
      background: #d1fae5;
      color: #065f46;
      padding: 10px 15px;
      border-radius: 6px;
      margin-bottom: 15px;
      display: none;
    }

    .content-translation-section {
      margin-top: 30px;
    }

    .content-translation-item {
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      margin-bottom: 24px;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .content-translation-item:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      border-color: #2563eb;
    }

    .content-header {
      background: linear-gradient(135deg, #f8fafc, #e2e8f0);
      padding: 20px 24px;
      border-bottom: 1px solid #e5e7eb;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .content-key-display {
      font-weight: 700;
      color: #1e293b;
      font-size: 18px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .content-key-display::before {
      content: "🏷️";
      font-size: 16px;
    }

    .content-original {
      background: linear-gradient(135deg, #fef3c7, #fde68a);
      padding: 20px 24px;
      border-bottom: 1px solid #e5e7eb;
    }

    .content-original-label {
      font-size: 13px;
      color: #92400e;
      font-weight: 700;
      margin-bottom: 8px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .content-original-label::before {
      content: "🇨🇳";
      font-size: 14px;
    }

    .content-original-text {
      font-size: 17px;
      color: #92400e;
      line-height: 1.6;
      font-weight: 500;
      background: rgba(255, 255, 255, 0.7);
      padding: 12px 16px;
      border-radius: 8px;
      border-left: 4px solid #f59e0b;
    }

    .translations-container {
      padding: 16px 24px 24px;
    }

    .translation-row {
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 16px;
      transition: all 0.3s ease;
    }

    .translation-row:hover {
      background: #f1f5f9;
      border-color: #cbd5e1;
    }

    .translation-row:last-child {
      margin-bottom: 0;
    }

    .translation-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .language-info {
      display: flex;
      align-items: center;
      gap: 10px;
      font-weight: 600;
      font-size: 15px;
      color: #374151;
    }

    .language-flag {
      font-size: 20px;
    }

    .translation-actions {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .translation-input-container {
      position: relative;
      margin-bottom: 8px;
    }

    .translation-input-large {
      width: 100%;
      padding: 14px 16px;
      border: 2px solid #d1d5db;
      border-radius: 8px;
      font-size: 15px;
      min-height: 50px;
      resize: vertical;
      transition: all 0.3s ease;
      font-family: inherit;
      line-height: 1.5;
    }

    .translation-input-large:focus {
      outline: none;
      border-color: #2563eb;
      box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
      background: white;
    }

    .translation-input-large:not(:placeholder-shown) {
      border-color: #10b981;
      background: #f0fdf4;
    }

    .ai-translate-btn {
      background: linear-gradient(135deg, #8b5cf6, #a855f7);
      color: white;
      border: none;
      padding: 10px 16px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 13px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 6px;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
    }

    .ai-translate-btn:hover {
      background: linear-gradient(135deg, #7c3aed, #9333ea);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(139, 92, 246, 0.4);
    }

    .ai-translate-btn:disabled {
      background: #9ca3af;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .delete-content-btn {
      background: linear-gradient(135deg, #ef4444, #dc2626);
      color: white;
      border: none;
      padding: 8px 14px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 5px;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
    }

    .delete-content-btn:hover {
      background: linear-gradient(135deg, #dc2626, #b91c1c);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(239, 68, 68, 0.4);
    }

    .ai-loading {
      display: inline-flex;
      align-items: center;
      gap: 6px;
      color: white;
      font-size: 13px;
      font-weight: 600;
    }

    .ai-loading .spinner {
      width: 14px;
      height: 14px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .translation-status {
      font-size: 12px;
      padding: 4px 10px;
      border-radius: 20px;
      font-weight: 600;
      display: inline-flex;
      align-items: center;
      gap: 4px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .status-manual {
      background: linear-gradient(135deg, #dbeafe, #bfdbfe);
      color: #1e40af;
      border: 1px solid #3b82f6;
    }

    .status-manual::before {
      content: "✏️";
      font-size: 10px;
    }

    .status-ai {
      background: linear-gradient(135deg, #ede9fe, #ddd6fe);
      color: #7c3aed;
      border: 1px solid #8b5cf6;
    }

    .status-ai::before {
      content: "🤖";
      font-size: 10px;
    }

    .status-empty {
      background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
      color: #6b7280;
      border: 1px solid #9ca3af;
    }

    .status-empty::before {
      content: "⏳";
      font-size: 10px;
    }

    .translation-progress {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      color: #6b7280;
      margin-top: 4px;
    }

    .progress-bar {
      flex: 1;
      height: 4px;
      background: #e5e7eb;
      border-radius: 2px;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #10b981, #059669);
      border-radius: 2px;
      transition: width 0.3s ease;
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #6b7280;
    }

    .empty-state-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .empty-state-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #374151;
    }

    .empty-state-description {
      font-size: 14px;
      line-height: 1.5;
    }

    /* 内容管理头部 */
    .content-management-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      flex-wrap: wrap;
      gap: 15px;
    }

    .content-controls {
      display: flex;
      gap: 15px;
      align-items: center;
      flex-wrap: wrap;
    }

    .search-filter-container {
      display: flex;
      gap: 10px;
      align-items: center;
    }

    .search-box {
      position: relative;
      min-width: 250px;
    }

    .search-box i {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: #6b7280;
      font-size: 14px;
    }

    .search-box input {
      padding-left: 40px;
      min-width: 250px;
    }

    .view-controls {
      display: flex;
      gap: 8px;
    }

    /* 统计信息 */
    .content-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 15px;
      margin-bottom: 25px;
      padding: 20px;
      background: linear-gradient(135deg, #f8fafc, #e2e8f0);
      border-radius: 10px;
      border: 1px solid #e5e7eb;
    }

    .stat-item {
      text-align: center;
      padding: 10px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .stat-number {
      display: block;
      font-size: 24px;
      font-weight: 700;
      color: #2563eb;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 12px;
      color: #6b7280;
      text-transform: uppercase;
      font-weight: 600;
      letter-spacing: 0.5px;
    }

    /* 折叠功能 */
    .content-translation-item {
      transition: all 0.3s ease;
    }

    .content-translation-item.collapsed .translations-container {
      display: none;
    }

    .content-translation-item.collapsed {
      margin-bottom: 8px;
    }

    .content-header {
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .content-header:hover {
      background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
    }

    .content-header::after {
      content: "▼";
      font-size: 12px;
      color: #6b7280;
      margin-left: 8px;
      transition: transform 0.3s ease;
    }

    .content-translation-item.collapsed .content-header::after {
      transform: rotate(-90deg);
    }

    /* 分页样式 */
    .pagination-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 30px;
      padding: 20px;
      background: #f8fafc;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
    }

    .pagination-info {
      font-size: 14px;
      color: #6b7280;
    }

    .pagination-controls {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .page-numbers {
      display: flex;
      gap: 5px;
    }

    .page-btn {
      padding: 6px 12px;
      border: 1px solid #d1d5db;
      background: white;
      color: #374151;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s ease;
    }

    .page-btn:hover {
      background: #f3f4f6;
      border-color: #9ca3af;
    }

    .page-btn.active {
      background: #2563eb;
      color: white;
      border-color: #2563eb;
    }

    .page-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    /* 隐藏状态 */
    .content-translation-item.hidden {
      display: none;
    }

    /* 响应式优化 */
    @media (max-width: 768px) {
      .content-management-header {
        flex-direction: column;
        align-items: stretch;
      }

      .content-controls {
        flex-direction: column;
        align-items: stretch;
      }

      .search-filter-container {
        flex-direction: column;
      }

      .search-box {
        min-width: auto;
      }

      .search-box input {
        min-width: auto;
      }

      .content-stats {
        grid-template-columns: repeat(2, 1fr);
      }

      .pagination-container {
        flex-direction: column;
        gap: 15px;
      }

      .pagination-controls {
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 头部 -->
    <div class="header">
      <h1>
        <i class="fas fa-globe"></i>
        简易多语言管理后台
      </h1>
      <p>管理系统支持的语言、菜单翻译和自定义内容翻译</p>
    </div>

    <!-- 标签页导航 -->
    <div class="tabs">
      <button class="tab active" onclick="switchTab('languages')">
        <i class="fas fa-language"></i>
        语言管理
      </button>
      <button class="tab" onclick="switchTab('menus')">
        <i class="fas fa-bars"></i>
        菜单翻译
      </button>
      <button class="tab" onclick="switchTab('content')">
        <i class="fas fa-file-text"></i>
        内容翻译
      </button>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 语言管理 -->
      <div id="languages" class="tab-content active">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <h2>语言管理</h2>
          <button class="btn btn-primary" onclick="openAddLanguageModal()">
            <i class="fas fa-plus"></i>
            新建语言
          </button>
        </div>

        <div class="language-grid" id="languageGrid">
          <!-- 语言卡片将通过JavaScript动态生成 -->
        </div>
      </div>

      <!-- 菜单翻译 -->
      <div id="menus" class="tab-content">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <h2>菜单翻译</h2>
          <div class="language-selector">
            <label>选择语言：</label>
            <select id="menuLanguageSelect" onchange="loadMenuTranslations()">
              <option value="">请选择语言</option>
            </select>
          </div>
        </div>

        <div class="success-message" id="menuSuccessMessage">
          菜单翻译保存成功！
        </div>

        <table class="translation-table" id="menuTable">
          <thead>
            <tr>
              <th>菜单项</th>
              <th>中文原文</th>
              <th>翻译</th>
            </tr>
          </thead>
          <tbody id="menuTableBody">
            <!-- 菜单翻译项将通过JavaScript动态生成 -->
          </tbody>
        </table>

        <div style="margin-top: 20px;">
          <button class="btn btn-success" onclick="saveMenuTranslations()">
            <i class="fas fa-save"></i>
            保存菜单翻译
          </button>
        </div>
      </div>

      <!-- 内容翻译 -->
      <div id="content" class="tab-content">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <h2>内容翻译</h2>
        </div>

        <!-- 添加新内容 -->
        <div class="add-content-form">
          <h3>添加新内容</h3>
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">内容标识</label>
              <input type="text" class="form-control" id="newContentKey" placeholder="例如: welcome_message">
            </div>
            <div class="form-group">
              <label class="form-label">中文内容</label>
              <input type="text" class="form-control" id="newContentText" placeholder="例如: 欢迎使用我们的系统">
            </div>
            <div>
              <button class="btn btn-primary" onclick="addNewContent()">
                <i class="fas fa-plus"></i>
                添加
              </button>
            </div>
          </div>
        </div>

        <div class="success-message" id="contentSuccessMessage">
          内容翻译保存成功！
        </div>

        <!-- 内容翻译列表 -->
        <div class="content-translation-section">
          <div class="content-management-header">
            <h3>内容翻译管理</h3>
            <div class="content-controls">
              <div class="search-filter-container">
                <div class="search-box">
                  <i class="fas fa-search"></i>
                  <input type="text" class="form-control" placeholder="搜索内容..." id="contentSearchInput" onkeyup="filterContent()">
                </div>
                <select class="form-control" id="statusFilter" onchange="filterContent()">
                  <option value="">全部状态</option>
                  <option value="completed">已完成</option>
                  <option value="partial">部分完成</option>
                  <option value="empty">未开始</option>
                </select>
              </div>
              <div class="view-controls">
                <button class="btn btn-secondary" onclick="toggleAllItems(false)">
                  <i class="fas fa-compress"></i>
                  全部折叠
                </button>
                <button class="btn btn-secondary" onclick="toggleAllItems(true)">
                  <i class="fas fa-expand"></i>
                  全部展开
                </button>
              </div>
            </div>
          </div>

          <div class="content-stats">
            <div class="stat-item">
              <span class="stat-number" id="totalItems">0</span>
              <span class="stat-label">总内容</span>
            </div>
            <div class="stat-item">
              <span class="stat-number" id="completedItems">0</span>
              <span class="stat-label">已完成</span>
            </div>
            <div class="stat-item">
              <span class="stat-number" id="partialItems">0</span>
              <span class="stat-label">部分完成</span>
            </div>
            <div class="stat-item">
              <span class="stat-number" id="emptyItems">0</span>
              <span class="stat-label">未开始</span>
            </div>
          </div>

          <div id="contentTranslationList">
            <!-- 内容翻译项将通过JavaScript动态生成 -->
          </div>

          <!-- 分页控件 -->
          <div class="pagination-container" id="paginationContainer">
            <div class="pagination-info">
              <span id="paginationInfo">显示 1-5 条，共 0 条记录</span>
            </div>
            <div class="pagination-controls">
              <button class="btn btn-sm btn-secondary" onclick="changePage(-1)" id="prevPageBtn">
                <i class="fas fa-chevron-left"></i>
                上一页
              </button>
              <div class="page-numbers" id="pageNumbers">
                <!-- 页码按钮将通过JavaScript生成 -->
              </div>
              <button class="btn btn-sm btn-secondary" onclick="changePage(1)" id="nextPageBtn">
                下一页
                <i class="fas fa-chevron-right"></i>
              </button>
            </div>
          </div>
        </div>

        <div style="margin-top: 20px;">
          <button class="btn btn-success" onclick="saveAllContentTranslations()">
            <i class="fas fa-save"></i>
            保存所有翻译
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 新建语言模态框 -->
  <div id="addLanguageModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>新建语言</h3>
        <button class="modal-close" onclick="closeAddLanguageModal()">&times;</button>
      </div>
      <form id="addLanguageForm">
        <div class="form-group">
          <label class="form-label">语言名称</label>
          <input type="text" class="form-control" id="languageName" placeholder="例如: 英语" required>
        </div>
        <div class="form-group">
          <label class="form-label">语言代码</label>
          <input type="text" class="form-control" id="languageCode" placeholder="例如: en-US" required>
        </div>
        <div class="form-group">
          <label class="form-label">语言图标</label>
          <input type="text" class="form-control" id="languageFlag" placeholder="例如: 🇺🇸" required>
        </div>
        <div style="display: flex; gap: 10px; justify-content: flex-end;">
          <button type="button" class="btn btn-secondary" onclick="closeAddLanguageModal()">取消</button>
          <button type="submit" class="btn btn-primary">创建语言</button>
        </div>
      </form>
    </div>
  </div>

  <script>
    // 数据存储
    let languages = [
      { name: '简体中文', code: 'zh-CN', flag: '🇨🇳', isDefault: true },
      { name: '英语', code: 'en-US', flag: '🇺🇸' },
      { name: '日语', code: 'ja-JP', flag: '🇯🇵' }
    ];

    // 固定菜单项
    const menuItems = [
      { key: 'dashboard', text: '仪表板' },
      { key: 'users', text: '用户管理' },
      { key: 'products', text: '产品管理' },
      { key: 'orders', text: '订单管理' },
      { key: 'settings', text: '系统设置' },
      { key: 'reports', text: '报表分析' },
      { key: 'help', text: '帮助中心' },
      { key: 'logout', text: '退出登录' }
    ];

    // 翻译数据
    let menuTranslations = {};
    let contentTranslations = {};
    let customContent = [
      { key: 'welcome_message', text: '欢迎使用我们的系统' },
      { key: 'success_message', text: '操作成功完成' },
      { key: 'error_message', text: '操作失败，请重试' },
      { key: 'login_title', text: '用户登录' },
      { key: 'register_title', text: '用户注册' },
      { key: 'forgot_password', text: '忘记密码' },
      { key: 'submit_button', text: '提交' },
      { key: 'cancel_button', text: '取消' },
      { key: 'save_button', text: '保存' },
      { key: 'delete_confirm', text: '确定要删除吗？' },
      { key: 'loading_message', text: '正在加载...' },
      { key: 'no_data_message', text: '暂无数据' }
    ];

    // 分页和过滤相关变量
    let currentPage = 1;
    let itemsPerPage = 5;
    let filteredContent = [];
    let searchTerm = '';
    let statusFilter = '';

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
      renderLanguages();
      updateLanguageSelectors();
      loadMenuTranslations();
      filterContent(); // 使用filterContent来初始化，这样会设置filteredContent
      updateStats();
    });

    // 标签页切换
    function switchTab(tabName) {
      // 更新标签页状态
      document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
      document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
      
      event.target.classList.add('active');
      document.getElementById(tabName).classList.add('active');
    }

    // 渲染语言列表
    function renderLanguages() {
      const grid = document.getElementById('languageGrid');
      grid.innerHTML = languages.map(lang => `
        <div class="language-card">
          <div class="language-flag">${lang.flag}</div>
          <div class="language-name">${lang.name}</div>
          <div class="language-code">${lang.code}</div>
          <div style="display: flex; gap: 10px; justify-content: center;">
            ${!lang.isDefault ? `<button class="btn btn-danger" onclick="deleteLanguage('${lang.code}')">
              <i class="fas fa-trash"></i>
              删除
            </button>` : '<span style="color: #6b7280;">默认语言</span>'}
          </div>
        </div>
      `).join('');
    }

    // 更新语言选择器
    function updateLanguageSelectors() {
      const menuSelect = document.getElementById('menuLanguageSelect');
      const contentSelect = document.getElementById('contentLanguageSelect');
      
      const options = languages.filter(lang => !lang.isDefault).map(lang => 
        `<option value="${lang.code}">${lang.flag} ${lang.name}</option>`
      ).join('');
      
      menuSelect.innerHTML = '<option value="">请选择语言</option>' + options;
      contentSelect.innerHTML = '<option value="">请选择语言</option>' + options;
    }

    // 新建语言
    function openAddLanguageModal() {
      document.getElementById('addLanguageModal').style.display = 'block';
    }

    function closeAddLanguageModal() {
      document.getElementById('addLanguageModal').style.display = 'none';
      document.getElementById('addLanguageForm').reset();
    }

    document.getElementById('addLanguageForm').addEventListener('submit', function(e) {
      e.preventDefault();
      
      const name = document.getElementById('languageName').value;
      const code = document.getElementById('languageCode').value;
      const flag = document.getElementById('languageFlag').value;
      
      // 检查语言代码是否已存在
      if (languages.find(lang => lang.code === code)) {
        alert('该语言代码已存在！');
        return;
      }
      
      languages.push({ name, code, flag });
      renderLanguages();
      updateLanguageSelectors();
      closeAddLanguageModal();
      
      alert('语言创建成功！');
    });

    // 删除语言
    function deleteLanguage(code) {
      if (confirm('确定要删除这个语言吗？相关的翻译数据也会被删除。')) {
        languages = languages.filter(lang => lang.code !== code);
        delete menuTranslations[code];
        delete contentTranslations[code];
        renderLanguages();
        updateLanguageSelectors();
        alert('语言删除成功！');
      }
    }

    // 加载菜单翻译
    function loadMenuTranslations() {
      const selectedLang = document.getElementById('menuLanguageSelect').value;
      const tbody = document.getElementById('menuTableBody');
      
      if (!selectedLang) {
        tbody.innerHTML = '<tr><td colspan="3" style="text-align: center; color: #6b7280;">请先选择要翻译的语言</td></tr>';
        return;
      }
      
      tbody.innerHTML = menuItems.map(item => `
        <tr>
          <td>${item.key}</td>
          <td>${item.text}</td>
          <td>
            <input type="text" class="translation-input" 
                   value="${menuTranslations[selectedLang]?.[item.key] || ''}"
                   onchange="updateMenuTranslation('${selectedLang}', '${item.key}', this.value)">
          </td>
        </tr>
      `).join('');
    }

    // 更新菜单翻译
    function updateMenuTranslation(langCode, key, value) {
      if (!menuTranslations[langCode]) {
        menuTranslations[langCode] = {};
      }
      menuTranslations[langCode][key] = value;
    }

    // 保存菜单翻译
    function saveMenuTranslations() {
      const message = document.getElementById('menuSuccessMessage');
      message.style.display = 'block';
      setTimeout(() => {
        message.style.display = 'none';
      }, 3000);
      
      console.log('菜单翻译数据:', menuTranslations);
    }

    // 过滤内容
    function filterContent() {
      searchTerm = document.getElementById('contentSearchInput').value.toLowerCase();
      statusFilter = document.getElementById('statusFilter').value;

      filteredContent = customContent.filter(item => {
        // 搜索过滤
        const matchesSearch = item.key.toLowerCase().includes(searchTerm) ||
                             item.text.toLowerCase().includes(searchTerm);

        // 状态过滤
        let matchesStatus = true;
        if (statusFilter) {
          const nonDefaultLanguages = languages.filter(lang => !lang.isDefault);
          const completedTranslations = nonDefaultLanguages.filter(lang =>
            contentTranslations[lang.code]?.[item.key]
          ).length;
          const totalTranslations = nonDefaultLanguages.length;

          if (statusFilter === 'completed') {
            matchesStatus = completedTranslations === totalTranslations && totalTranslations > 0;
          } else if (statusFilter === 'partial') {
            matchesStatus = completedTranslations > 0 && completedTranslations < totalTranslations;
          } else if (statusFilter === 'empty') {
            matchesStatus = completedTranslations === 0;
          }
        }

        return matchesSearch && matchesStatus;
      });

      currentPage = 1;
      loadContentTranslations();
      updateStats();
    }

    // 加载内容翻译
    function loadContentTranslations() {
      const contentList = document.getElementById('contentTranslationList');

      // 如果没有过滤结果，使用全部内容
      const contentToShow = filteredContent.length > 0 || searchTerm || statusFilter ? filteredContent : customContent;

      if (contentToShow.length === 0) {
        contentList.innerHTML = `
          <div class="empty-state">
            <div class="empty-state-icon">📝</div>
            <div class="empty-state-title">${customContent.length === 0 ? '暂无翻译内容' : '没有找到匹配的内容'}</div>
            <div class="empty-state-description">${customContent.length === 0 ? '请先在上方添加需要翻译的内容项，然后就可以开始翻译工作了' : '请尝试调整搜索条件或过滤器'}</div>
          </div>
        `;
        updatePagination(0);
        return;
      }

      // 分页计算
      const totalItems = contentToShow.length;
      const totalPages = Math.ceil(totalItems / itemsPerPage);
      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
      const currentItems = contentToShow.slice(startIndex, endIndex);

      contentList.innerHTML = currentItems.map(item => {
        const nonDefaultLanguages = languages.filter(lang => !lang.isDefault);
        const totalTranslations = nonDefaultLanguages.length;
        const completedTranslations = nonDefaultLanguages.filter(lang =>
          contentTranslations[lang.code]?.[item.key]
        ).length;
        const progressPercentage = totalTranslations > 0 ? (completedTranslations / totalTranslations) * 100 : 0;

        return `
          <div class="content-translation-item" data-key="${item.key}">
            <div class="content-header" onclick="toggleItem('${item.key}')">
              <div class="content-key-display">${item.key}</div>
              <button class="delete-content-btn" onclick="event.stopPropagation(); deleteContent('${item.key}')">
                <i class="fas fa-trash"></i>
                删除
              </button>
            </div>

            <div class="content-original">
              <div class="content-original-label">中文原文</div>
              <div class="content-original-text">${item.text}</div>
              <div class="translation-progress">
                <span>翻译进度:</span>
                <div class="progress-bar">
                  <div class="progress-fill" style="width: ${progressPercentage}%"></div>
                </div>
                <span>${completedTranslations}/${totalTranslations}</span>
              </div>
            </div>

            <div class="translations-container">
              ${nonDefaultLanguages.map(lang => {
                const currentTranslation = contentTranslations[lang.code]?.[item.key] || '';
                const translationStatus = getTranslationStatus(lang.code, item.key);

                return `
                  <div class="translation-row">
                    <div class="translation-header">
                      <div class="language-info">
                        <span class="language-flag">${lang.flag}</span>
                        <span>${lang.name}</span>
                      </div>
                      <div class="translation-actions">
                        <div class="translation-status ${translationStatus.class}">${translationStatus.text}</div>
                        <button class="ai-translate-btn"
                                onclick="aiTranslate('${lang.code}', '${item.key}', '${item.text}')"
                                id="ai_btn_${lang.code}_${item.key}">
                          <i class="fas fa-robot"></i>
                          AI翻译
                        </button>
                      </div>
                    </div>
                    <div class="translation-input-container">
                      <textarea class="translation-input-large"
                               placeholder="请输入${lang.name}翻译内容，或点击上方AI翻译按钮自动生成"
                               onchange="updateContentTranslation('${lang.code}', '${item.key}', this.value, 'manual')"
                               id="translation_${lang.code}_${item.key}">${currentTranslation}</textarea>
                    </div>
                  </div>
                `;
              }).join('')}
            </div>
          </div>
        `;
      }).join('');

      updatePagination(totalItems);
    }

    // 获取翻译状态
    function getTranslationStatus(langCode, key) {
      const translation = contentTranslations[langCode]?.[key];
      const translationMeta = contentTranslations[langCode]?.[`${key}_meta`];

      if (!translation) {
        return { class: 'status-empty', text: '未翻译' };
      }

      if (translationMeta?.type === 'ai') {
        return { class: 'status-ai', text: 'AI翻译' };
      }

      return { class: 'status-manual', text: '手动翻译' };
    }

    // 更新分页
    function updatePagination(totalItems) {
      const paginationContainer = document.getElementById('paginationContainer');
      const paginationInfo = document.getElementById('paginationInfo');
      const pageNumbers = document.getElementById('pageNumbers');
      const prevBtn = document.getElementById('prevPageBtn');
      const nextBtn = document.getElementById('nextPageBtn');

      if (totalItems === 0) {
        paginationContainer.style.display = 'none';
        return;
      }

      paginationContainer.style.display = 'flex';

      const totalPages = Math.ceil(totalItems / itemsPerPage);
      const startIndex = (currentPage - 1) * itemsPerPage + 1;
      const endIndex = Math.min(currentPage * itemsPerPage, totalItems);

      paginationInfo.textContent = `显示 ${startIndex}-${endIndex} 条，共 ${totalItems} 条记录`;

      // 更新按钮状态
      prevBtn.disabled = currentPage === 1;
      nextBtn.disabled = currentPage === totalPages;

      // 生成页码按钮
      pageNumbers.innerHTML = '';
      for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
          const pageBtn = document.createElement('button');
          pageBtn.className = `page-btn ${i === currentPage ? 'active' : ''}`;
          pageBtn.textContent = i;
          pageBtn.onclick = () => goToPage(i);
          pageNumbers.appendChild(pageBtn);
        } else if (i === currentPage - 3 || i === currentPage + 3) {
          const dots = document.createElement('span');
          dots.textContent = '...';
          dots.style.padding = '6px 8px';
          dots.style.color = '#6b7280';
          pageNumbers.appendChild(dots);
        }
      }
    }

    // 分页控制
    function changePage(direction) {
      const totalPages = Math.ceil((filteredContent.length || customContent.length) / itemsPerPage);
      const newPage = currentPage + direction;

      if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        loadContentTranslations();
      }
    }

    function goToPage(page) {
      currentPage = page;
      loadContentTranslations();
    }

    // 折叠功能
    function toggleItem(key) {
      const item = document.querySelector(`[data-key="${key}"]`);
      if (item) {
        item.classList.toggle('collapsed');
      }
    }

    function toggleAllItems(expand) {
      const items = document.querySelectorAll('.content-translation-item');
      items.forEach(item => {
        if (expand) {
          item.classList.remove('collapsed');
        } else {
          item.classList.add('collapsed');
        }
      });
    }

    // 更新统计信息
    function updateStats() {
      const nonDefaultLanguages = languages.filter(lang => !lang.isDefault);
      const totalLanguages = nonDefaultLanguages.length;

      let totalItems = customContent.length;
      let completedItems = 0;
      let partialItems = 0;
      let emptyItems = 0;

      customContent.forEach(item => {
        const completedTranslations = nonDefaultLanguages.filter(lang =>
          contentTranslations[lang.code]?.[item.key]
        ).length;

        if (completedTranslations === totalLanguages && totalLanguages > 0) {
          completedItems++;
        } else if (completedTranslations > 0) {
          partialItems++;
        } else {
          emptyItems++;
        }
      });

      document.getElementById('totalItems').textContent = totalItems;
      document.getElementById('completedItems').textContent = completedItems;
      document.getElementById('partialItems').textContent = partialItems;
      document.getElementById('emptyItems').textContent = emptyItems;
    }

    // 更新内容翻译
    function updateContentTranslation(langCode, key, value, type = 'manual') {
      if (!contentTranslations[langCode]) {
        contentTranslations[langCode] = {};
      }
      contentTranslations[langCode][key] = value;
      contentTranslations[langCode][`${key}_meta`] = { type, timestamp: new Date().toISOString() };

      // 更新状态显示和进度条
      setTimeout(() => {
        const statusElement = document.querySelector(`#translation_${langCode}_${key}`).closest('.translation-row').querySelector('.translation-status');
        const status = getTranslationStatus(langCode, key);
        statusElement.className = `translation-status ${status.class}`;
        statusElement.textContent = status.text;

        // 更新进度条和统计
        updateProgressBar(key);
        updateStats();
      }, 100);
    }

    // 更新进度条
    function updateProgressBar(contentKey) {
      const nonDefaultLanguages = languages.filter(lang => !lang.isDefault);
      const totalTranslations = nonDefaultLanguages.length;
      const completedTranslations = nonDefaultLanguages.filter(lang =>
        contentTranslations[lang.code]?.[contentKey]
      ).length;
      const progressPercentage = totalTranslations > 0 ? (completedTranslations / totalTranslations) * 100 : 0;

      // 找到对应的进度条元素
      const contentItem = document.querySelector(`[data-key="${contentKey}"]`);
      if (contentItem) {
        const progressFill = contentItem.querySelector('.progress-fill');
        const progressText = contentItem.querySelector('.translation-progress span:last-child');

        if (progressFill && progressText) {
          progressFill.style.width = `${progressPercentage}%`;
          progressText.textContent = `${completedTranslations}/${totalTranslations}`;
        }
      }
    }

    // 添加新内容
    function addNewContent() {
      const key = document.getElementById('newContentKey').value.trim();
      const text = document.getElementById('newContentText').value.trim();

      if (!key || !text) {
        alert('请填写完整的内容标识和中文内容');
        return;
      }

      if (customContent.find(item => item.key === key)) {
        alert('该内容标识已存在！');
        return;
      }

      customContent.push({ key, text });
      document.getElementById('newContentKey').value = '';
      document.getElementById('newContentText').value = '';

      // 重新过滤和加载内容
      filterContent();
      updateStats();

      alert('内容添加成功！');
    }

    // 删除内容
    function deleteContent(key) {
      if (confirm('确定要删除这个内容吗？相关的所有翻译也会被删除。')) {
        customContent = customContent.filter(item => item.key !== key);

        // 删除所有语言中的相关翻译和元数据
        Object.keys(contentTranslations).forEach(langCode => {
          delete contentTranslations[langCode][key];
          delete contentTranslations[langCode][`${key}_meta`];
        });

        // 重新过滤和加载内容
        filterContent();
        updateStats();
        alert('内容删除成功！');
      }
    }

    // AI翻译功能
    async function aiTranslate(langCode, key, sourceText) {
      const button = document.getElementById(`ai_btn_${langCode}_${key}`);
      const textarea = document.getElementById(`translation_${langCode}_${key}`);

      // 显示加载状态
      button.disabled = true;
      button.innerHTML = '<div class="ai-loading"><div class="spinner"></div>翻译中...</div>';

      try {
        // 模拟AI翻译API调用
        const translation = await simulateAITranslation(sourceText, langCode);

        // 更新翻译内容
        textarea.value = translation;
        updateContentTranslation(langCode, key, translation, 'ai');

        // 显示成功状态
        button.innerHTML = '<i class="fas fa-check"></i>翻译完成';
        setTimeout(() => {
          button.disabled = false;
          button.innerHTML = '<i class="fas fa-robot"></i>AI翻译';
        }, 2000);

      } catch (error) {
        console.error('AI翻译失败:', error);
        button.innerHTML = '<i class="fas fa-exclamation-triangle"></i>翻译失败';
        setTimeout(() => {
          button.disabled = false;
          button.innerHTML = '<i class="fas fa-robot"></i>AI翻译';
        }, 2000);
      }
    }

    // 模拟AI翻译API
    async function simulateAITranslation(text, targetLang) {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1000));

      // 简单的翻译映射（实际项目中应该调用真实的翻译API）
      const translations = {
        'en-US': {
          '欢迎使用我们的系统': 'Welcome to our system',
          '操作成功完成': 'Operation completed successfully',
          '操作失败，请重试': 'Operation failed, please try again',
          '仪表板': 'Dashboard',
          '用户管理': 'User Management',
          '产品管理': 'Product Management',
          '订单管理': 'Order Management',
          '系统设置': 'System Settings',
          '报表分析': 'Report Analysis',
          '帮助中心': 'Help Center',
          '退出登录': 'Logout'
        },
        'ja-JP': {
          '欢迎使用我们的系统': '私たちのシステムへようこそ',
          '操作成功完成': '操作が正常に完了しました',
          '操作失败，请重试': '操作に失敗しました。再試行してください',
          '仪表板': 'ダッシュボード',
          '用户管理': 'ユーザー管理',
          '产品管理': '製品管理',
          '订单管理': '注文管理',
          '系统设置': 'システム設定',
          '报表分析': 'レポート分析',
          '帮助中心': 'ヘルプセンター',
          '退出登录': 'ログアウト'
        }
      };

      return translations[targetLang]?.[text] || `[AI翻译] ${text}`;
    }

    // 保存所有内容翻译
    function saveAllContentTranslations() {
      const message = document.getElementById('contentSuccessMessage');
      message.style.display = 'block';
      setTimeout(() => {
        message.style.display = 'none';
      }, 3000);

      console.log('内容翻译数据:', contentTranslations);

      // 这里可以添加实际的保存逻辑，比如发送到服务器
      // fetch('/api/save-translations', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(contentTranslations)
      // });
    }

    // 点击模态框外部关闭
    window.onclick = function(event) {
      const modal = document.getElementById('addLanguageModal');
      if (event.target === modal) {
        closeAddLanguageModal();
      }
    }
  </script>
</body>
</html>
