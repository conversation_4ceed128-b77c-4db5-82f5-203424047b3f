<!-- 通用头部组件 -->
<header class="header">
  <!-- 左侧：面包屑导航 -->
  <div class="header-left">
    <button class="menu-toggle d-md-none">
      <i class="fas fa-bars"></i>
    </button>
    <nav class="breadcrumb">
      <ol class="breadcrumb-list">
        <li class="breadcrumb-item">
          <a href="#" class="breadcrumb-link">
            <i class="fas fa-home"></i>
          </a>
        </li>
        <li class="breadcrumb-item">
          <span class="breadcrumb-current">仪表板</span>
        </li>
      </ol>
    </nav>
  </div>

  <!-- 右侧：工具栏 -->
  <div class="header-right">
    <!-- 语言切换 -->
    <div class="header-item">
      <select class="language-selector form-control">
        <option value="zh-CN">简体中文</option>
        <option value="zh-TW">繁体中文</option>
        <option value="en-US">English</option>
        <option value="ja-JP">日本語</option>
        <option value="ko-KR">한국어</option>
        <option value="fr-FR">Français</option>
        <option value="de-DE">Deutsch</option>
        <option value="es-ES">Español</option>
      </select>
    </div>

    <!-- 通知 -->
    <div class="header-item">
      <button class="notification-btn" onclick="toggleNotifications()">
        <i class="fas fa-bell"></i>
        <span class="notification-badge">3</span>
      </button>
      
      <!-- 通知下拉菜单 -->
      <div class="notification-dropdown" id="notificationDropdown">
        <div class="notification-header">
          <h4>通知</h4>
          <button class="btn btn-sm btn-secondary">全部标记为已读</button>
        </div>
        <div class="notification-list">
          <div class="notification-item unread">
            <div class="notification-icon">
              <i class="fas fa-info-circle text-info"></i>
            </div>
            <div class="notification-content">
              <div class="notification-title">新项目创建</div>
              <div class="notification-text">项目"电商平台"已创建，等待翻译</div>
              <div class="notification-time">2分钟前</div>
            </div>
          </div>
          <div class="notification-item unread">
            <div class="notification-icon">
              <i class="fas fa-check-circle text-success"></i>
            </div>
            <div class="notification-content">
              <div class="notification-title">翻译完成</div>
              <div class="notification-text">英文翻译已完成审核</div>
              <div class="notification-time">1小时前</div>
            </div>
          </div>
          <div class="notification-item">
            <div class="notification-icon">
              <i class="fas fa-exclamation-triangle text-warning"></i>
            </div>
            <div class="notification-content">
              <div class="notification-title">翻译质量警告</div>
              <div class="notification-text">日文翻译质量需要改进</div>
              <div class="notification-time">3小时前</div>
            </div>
          </div>
        </div>
        <div class="notification-footer">
          <a href="#" class="btn btn-sm btn-primary w-100">查看所有通知</a>
        </div>
      </div>
    </div>

    <!-- 用户菜单 -->
    <div class="header-item">
      <div class="user-menu">
        <button class="user-menu-btn" onclick="toggleUserMenu()">
          <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" 
               alt="用户头像" class="user-avatar">
          <span class="user-name">张三</span>
          <i class="fas fa-chevron-down"></i>
        </button>
        
        <!-- 用户下拉菜单 -->
        <div class="user-dropdown" id="userDropdown">
          <div class="user-info">
            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face" 
                 alt="用户头像" class="user-avatar-large">
            <div class="user-details">
              <div class="user-name">张三</div>
              <div class="user-email"><EMAIL></div>
              <div class="user-role">系统管理员</div>
            </div>
          </div>
          <div class="user-menu-divider"></div>
          <div class="user-menu-items">
            <a href="#" class="user-menu-item">
              <i class="fas fa-user"></i>
              <span>个人资料</span>
            </a>
            <a href="#" class="user-menu-item">
              <i class="fas fa-cog"></i>
              <span>账户设置</span>
            </a>
            <a href="#" class="user-menu-item">
              <i class="fas fa-question-circle"></i>
              <span>帮助中心</span>
            </a>
          </div>
          <div class="user-menu-divider"></div>
          <a href="#" class="user-menu-item text-danger">
            <i class="fas fa-sign-out-alt"></i>
            <span>退出登录</span>
          </a>
        </div>
      </div>
    </div>
  </div>
</header>

<style>
/* 头部样式 */
.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.header-item {
  position: relative;
}

/* 菜单切换按钮 */
.menu-toggle {
  background: none;
  border: none;
  font-size: 18px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
}

.menu-toggle:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* 面包屑导航 */
.breadcrumb-list {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  margin: 0 var(--spacing-sm);
  color: var(--text-tertiary);
}

.breadcrumb-link {
  color: var(--text-secondary);
  text-decoration: none;
}

.breadcrumb-link:hover {
  color: var(--primary-color);
}

.breadcrumb-current {
  color: var(--text-primary);
  font-weight: 500;
}

/* 语言选择器 */
.language-selector {
  min-width: 120px;
  font-size: 12px;
}

/* 通知按钮 */
.notification-btn {
  position: relative;
  background: none;
  border: none;
  font-size: 18px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
}

.notification-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.notification-badge {
  position: absolute;
  top: 2px;
  right: 2px;
  background: var(--danger-color);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

/* 通知下拉菜单 */
.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 320px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  display: none;
}

.notification-dropdown.show {
  display: block;
}

.notification-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background: var(--bg-tertiary);
}

.notification-item.unread {
  background: rgba(79, 70, 229, 0.05);
}

.notification-icon {
  margin-right: var(--spacing-sm);
  font-size: 16px;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 500;
  margin-bottom: var(--spacing-xs);
}

.notification-text {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.notification-time {
  font-size: 11px;
  color: var(--text-tertiary);
}

.notification-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
}

/* 用户菜单 */
.user-menu-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  transition: background-color 0.2s ease;
}

.user-menu-btn:hover {
  background: var(--bg-tertiary);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-avatar-large {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

/* 用户下拉菜单 */
.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 280px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  display: none;
}

.user-dropdown.show {
  display: block;
}

.user-info {
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.user-details .user-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.user-email {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.user-role {
  font-size: 11px;
  color: var(--text-tertiary);
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  display: inline-block;
}

.user-menu-divider {
  height: 1px;
  background: var(--border-color);
  margin: 0 var(--spacing-md);
}

.user-menu-items {
  padding: var(--spacing-sm) 0;
}

.user-menu-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  color: var(--text-primary);
  text-decoration: none;
  transition: background-color 0.2s ease;
}

.user-menu-item:hover {
  background: var(--bg-tertiary);
}

.user-menu-item.text-danger {
  color: var(--danger-color);
}

/* 响应式 */
@media (max-width: 768px) {
  .header-left .breadcrumb {
    display: none;
  }
  
  .user-name {
    display: none;
  }
}
</style>

<script>
// 切换通知下拉菜单
function toggleNotifications() {
  const dropdown = document.getElementById('notificationDropdown');
  dropdown.classList.toggle('show');
  
  // 关闭用户菜单
  document.getElementById('userDropdown').classList.remove('show');
}

// 切换用户菜单
function toggleUserMenu() {
  const dropdown = document.getElementById('userDropdown');
  dropdown.classList.toggle('show');
  
  // 关闭通知菜单
  document.getElementById('notificationDropdown').classList.remove('show');
}

// 点击外部关闭下拉菜单
document.addEventListener('click', (e) => {
  if (!e.target.closest('.notification-btn') && !e.target.closest('.notification-dropdown')) {
    document.getElementById('notificationDropdown').classList.remove('show');
  }
  
  if (!e.target.closest('.user-menu-btn') && !e.target.closest('.user-dropdown')) {
    document.getElementById('userDropdown').classList.remove('show');
  }
});
</script>
