// 多语言管理后台 - 通用JavaScript

// 全局配置
const CONFIG = {
  API_BASE_URL: '/api',
  SUPPORTED_LANGUAGES: {
    'zh-CN': '简体中文',
    'zh-TW': '繁体中文',
    'en-US': 'English',
    'ja-JP': '日本語',
    'ko-KR': '한국어',
    'fr-FR': 'Français',
    'de-DE': 'Deutsch',
    'es-ES': 'Español'
  }
};

// 工具函数
const Utils = {
  // 格式化日期
  formatDate(date) {
    if (!date) return '-';
    const d = new Date(date);
    return d.toLocaleDateString('zh-CN') + ' ' + d.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  },

  // 显示通知
  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <i class="fas fa-${this.getNotificationIcon(type)}"></i>
        <span>${message}</span>
      </div>
      <button class="notification-close" onclick="this.parentElement.remove()">
        <i class="fas fa-times"></i>
      </button>
    `;
    
    document.body.appendChild(notification);
    
    // 自动移除
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 5000);
  },

  getNotificationIcon(type) {
    const icons = {
      'success': 'check-circle',
      'error': 'exclamation-circle',
      'warning': 'exclamation-triangle',
      'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
  },

  // 确认对话框
  confirm(message, callback) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
      <div class="modal">
        <div class="modal-header">
          <h3>确认操作</h3>
        </div>
        <div class="modal-body">
          <p>${message}</p>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
          <button class="btn btn-danger" onclick="confirmAction()">确认</button>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    window.confirmAction = () => {
      modal.remove();
      callback();
      delete window.confirmAction;
    };
  },

  // 加载状态
  showLoading(element) {
    element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 加载中...';
    element.disabled = true;
  },

  hideLoading(element, originalText) {
    element.innerHTML = originalText;
    element.disabled = false;
  }
};

// 导航管理
const Navigation = {
  init() {
    this.setActiveNav();
    this.bindEvents();
  },

  setActiveNav() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
      link.classList.remove('active');
      if (link.getAttribute('href') === currentPath || 
          (currentPath.includes(link.getAttribute('href')) && link.getAttribute('href') !== '/')) {
        link.classList.add('active');
      }
    });
  },

  bindEvents() {
    // 移动端菜单切换
    const menuToggle = document.querySelector('.menu-toggle');
    const sidebar = document.querySelector('.sidebar');
    
    if (menuToggle && sidebar) {
      menuToggle.addEventListener('click', () => {
        sidebar.classList.toggle('show');
      });
    }

    // 点击外部关闭菜单
    document.addEventListener('click', (e) => {
      if (window.innerWidth <= 768 && sidebar && !sidebar.contains(e.target) && !e.target.closest('.menu-toggle')) {
        sidebar.classList.remove('show');
      }
    });
  }
};

// 多语言管理
const I18n = {
  currentLanguage: 'zh-CN',
  translations: {},

  init() {
    this.currentLanguage = localStorage.getItem('language') || 'zh-CN';
    this.loadTranslations();
    this.updateLanguageSelector();
  },

  setLanguage(lang) {
    this.currentLanguage = lang;
    localStorage.setItem('language', lang);
    this.loadTranslations();
    this.updatePage();
  },

  loadTranslations() {
    // 这里应该从服务器加载翻译文件
    // 暂时使用模拟数据
    this.translations = {
      'zh-CN': {
        'dashboard': '仪表板',
        'projects': '项目管理',
        'translations': '翻译管理',
        'settings': '设置'
      },
      'en-US': {
        'dashboard': 'Dashboard',
        'projects': 'Projects',
        'translations': 'Translations',
        'settings': 'Settings'
      }
    };
  },

  t(key) {
    return this.translations[this.currentLanguage]?.[key] || key;
  },

  updatePage() {
    document.querySelectorAll('[data-i18n]').forEach(element => {
      const key = element.getAttribute('data-i18n');
      element.textContent = this.t(key);
    });
  },

  updateLanguageSelector() {
    const selector = document.querySelector('.language-selector');
    if (selector) {
      selector.value = this.currentLanguage;
    }
  }
};

// 表格管理
const TableManager = {
  init() {
    this.bindSortEvents();
    this.bindFilterEvents();
  },

  bindSortEvents() {
    document.querySelectorAll('.sortable').forEach(header => {
      header.addEventListener('click', (e) => {
        this.sortTable(e.target);
      });
    });
  },

  sortTable(header) {
    const table = header.closest('table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentElement.children).indexOf(header);
    const isAscending = !header.classList.contains('sort-asc');

    rows.sort((a, b) => {
      const aValue = a.children[columnIndex].textContent.trim();
      const bValue = b.children[columnIndex].textContent.trim();
      
      if (isAscending) {
        return aValue.localeCompare(bValue);
      } else {
        return bValue.localeCompare(aValue);
      }
    });

    // 更新排序图标
    header.parentElement.querySelectorAll('th').forEach(th => {
      th.classList.remove('sort-asc', 'sort-desc');
    });
    header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');

    // 重新排列行
    rows.forEach(row => tbody.appendChild(row));
  },

  bindFilterEvents() {
    document.querySelectorAll('.table-filter').forEach(filter => {
      filter.addEventListener('input', (e) => {
        this.filterTable(e.target);
      });
    });
  },

  filterTable(input) {
    const table = input.closest('.card').querySelector('table');
    const tbody = table.querySelector('tbody');
    const rows = tbody.querySelectorAll('tr');
    const filterValue = input.value.toLowerCase();

    rows.forEach(row => {
      const text = row.textContent.toLowerCase();
      row.style.display = text.includes(filterValue) ? '' : 'none';
    });
  }
};

// 页面初始化
document.addEventListener('DOMContentLoaded', () => {
  Navigation.init();
  I18n.init();
  TableManager.init();
});

// 语言切换事件
document.addEventListener('change', (e) => {
  if (e.target.classList.contains('language-selector')) {
    I18n.setLanguage(e.target.value);
  }
});
