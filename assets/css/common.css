/* 多语言管理后台 - 通用样式 */

:root {
  /* 主色调 */
  --primary-color: #4F46E5;
  --primary-light: #6366F1;
  --primary-dark: #3730A3;
  
  /* 辅助色 */
  --success-color: #10B981;
  --warning-color: #F59E0B;
  --danger-color: #EF4444;
  --info-color: #3B82F6;
  
  /* 中性色 */
  --gray-50: #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --gray-900: #111827;
  
  /* 背景色 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F9FAFB;
  --bg-tertiary: #F3F4F6;
  
  /* 文字色 */
  --text-primary: #111827;
  --text-secondary: #6B7280;
  --text-tertiary: #9CA3AF;
  
  /* 边框 */
  --border-color: #E5E7EB;
  --border-radius: 8px;
  --border-radius-sm: 4px;
  --border-radius-lg: 12px;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
}

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
}

/* 布局 */
.layout {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 260px;
  background: var(--bg-primary);
  border-right: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  z-index: 1000;
}

.main-content {
  flex: 1;
  margin-left: 260px;
  display: flex;
  flex-direction: column;
}

.header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: 0 var(--spacing-lg);
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-sm);
}

.content {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
}

/* 导航栏样式 */
.nav-brand {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.nav-brand h1 {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color);
}

.nav-menu {
  padding: var(--spacing-md) 0;
}

.nav-item {
  margin: 0 var(--spacing-md);
}

.nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
}

.nav-link:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.nav-link.active {
  background: var(--primary-color);
  color: white;
}

.nav-link i {
  margin-right: var(--spacing-sm);
  width: 16px;
  text-align: center;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: var(--spacing-xs);
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-dark);
}

.btn-secondary {
  background: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.btn-secondary:hover {
  background: var(--bg-tertiary);
}

.btn-success {
  background: var(--success-color);
  color: white;
}

.btn-danger {
  background: var(--danger-color);
  color: white;
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 12px;
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 16px;
}

/* 卡片样式 */
.card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.card-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.card-body {
  padding: var(--spacing-lg);
}

/* 表格样式 */
.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table th,
.table td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.table th {
  background: var(--bg-secondary);
  font-weight: 600;
  color: var(--text-primary);
}

.table tbody tr:hover {
  background: var(--bg-tertiary);
}

/* 表单样式 */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-primary);
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 工具类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-muted { color: var(--text-secondary); }
.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-danger { color: var(--danger-color); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

.d-flex { display: flex; }
.align-items-center { align-items: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-end { justify-content: flex-end; }

.w-100 { width: 100%; }

/* 页面头部 */
.page-header {
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.page-description {
  color: var(--text-secondary);
  margin: 0;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.stat-icon {
  font-size: 32px;
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.stat-change {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.stat-change.positive {
  color: var(--success-color);
}

.stat-change.negative {
  color: var(--danger-color);
}

.stat-change.neutral {
  color: var(--text-tertiary);
}

/* 仪表板网格 */
.dashboard-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-lg);
}

.chart-card {
  grid-column: 1 / -1;
}

/* 图表样式 */
.chart-placeholder {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-mock {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.chart-bars {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 250px;
  padding: 0 var(--spacing-lg);
}

.chart-bar {
  width: 40px;
  background: linear-gradient(to top, var(--primary-color), var(--primary-light));
  border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
  min-height: 20px;
}

.chart-labels {
  display: flex;
  justify-content: space-around;
  padding: var(--spacing-sm) var(--spacing-lg) 0;
  font-size: 12px;
  color: var(--text-secondary);
}

/* 语言统计 */
.language-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.language-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.language-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.language-name {
  font-weight: 500;
  color: var(--text-primary);
}

.language-count {
  font-size: 12px;
  color: var(--text-secondary);
}

.language-progress {
  height: 6px;
  background: var(--bg-tertiary);
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(to right, var(--primary-color), var(--primary-light));
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* 活动列表 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.activity-item {
  display: flex;
  gap: var(--spacing-sm);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.activity-icon {
  font-size: 16px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  border-radius: 50%;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.activity-description {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.activity-time {
  font-size: 11px;
  color: var(--text-tertiary);
}

/* 系统状态 */
.system-status {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-indicator.online {
  background: var(--success-color);
}

.status-indicator.warning {
  background: var(--warning-color);
}

.status-indicator.offline {
  background: var(--danger-color);
}

.status-info {
  flex: 1;
}

.status-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.status-value {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 筛选行 */
.filter-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr auto;
  gap: var(--spacing-md);
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.search-box {
  position: relative;
}

.search-box i {
  position: absolute;
  left: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
}

.search-box input {
  padding-left: 32px;
}

/* 公司信息 */
.company-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.company-logo {
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius-sm);
  object-fit: cover;
}

.company-name {
  font-weight: 500;
  color: var(--text-primary);
}

.company-code {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 状态徽章 */
.status-badge {
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.active {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-badge.inactive {
  background: rgba(156, 163, 175, 0.1);
  color: var(--text-secondary);
}

.status-badge.suspended {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

/* 徽章 */
.badge {
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  font-size: 12px;
  font-weight: 500;
}

.badge-info {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: var(--spacing-xs);
}

/* 分页 */
.pagination-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

.pagination-info {
  font-size: 14px;
  color: var(--text-secondary);
}

.pagination {
  display: flex;
  gap: var(--spacing-xs);
  align-items: center;
}

.pagination-dots {
  padding: 0 var(--spacing-xs);
  color: var(--text-tertiary);
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal {
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-lg {
  max-width: 800px;
}

.modal-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 18px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius);
}

.modal-close:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-lg);
}

.modal-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

/* 表单行 */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

/* 表格响应式 */
.table-responsive {
  overflow-x: auto;
}

/* 排序图标 */
.sortable {
  cursor: pointer;
  user-select: none;
}

.sortable:hover {
  background: var(--bg-tertiary);
}

.sortable i {
  margin-left: var(--spacing-xs);
  opacity: 0.5;
}

.sortable.sort-asc i::before {
  content: '\f0de';
}

.sortable.sort-desc i::before {
  content: '\f0dd';
}

/* 语言信息 */
.language-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.language-flag {
  font-size: 20px;
}

.language-name {
  font-weight: 500;
  color: var(--text-primary);
}

.language-native {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 质量指示器 */
.quality-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.quality-bar {
  width: 60px;
  height: 6px;
  background: var(--bg-tertiary);
  border-radius: 3px;
  overflow: hidden;
}

.quality-fill {
  height: 100%;
  background: linear-gradient(to right, var(--danger-color), var(--warning-color), var(--success-color));
  border-radius: 3px;
  transition: width 0.3s ease;
}

.quality-text {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
}

/* 规则网格 */
.rules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.rule-item {
  padding: var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-secondary);
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.rule-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.rule-description {
  margin: 0;
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 开关按钮 */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--gray-300);
  transition: 0.3s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
  background-color: var(--primary-color);
}

input:checked + .slider:before {
  transform: translateX(20px);
}

/* 代码样式 */
code {
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: var(--primary-color);
}

/* 翻译工作台样式 */
.workbench-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.project-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.project-meta {
  display: flex;
  gap: var(--spacing-md);
  font-size: 14px;
}

.project-status {
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  font-size: 12px;
  font-weight: 500;
}

.project-status.active {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.project-progress {
  color: var(--text-secondary);
}

.project-deadline {
  color: var(--text-secondary);
}

.workbench-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* 语言选择栏 */
.language-selector-bar {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 2fr;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
  align-items: end;
}

.source-language,
.target-language,
.filter-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.source-language label,
.target-language label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
}

/* 翻译工作区 */
.translation-workspace {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: var(--spacing-lg);
  height: calc(100vh - 300px);
}

/* 翻译列表 */
.translation-list {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.list-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.item-count {
  color: var(--text-secondary);
  font-weight: normal;
}

.list-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.translation-items {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-sm);
}

.translation-item {
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

.translation-item:hover {
  background: var(--bg-tertiary);
  border-color: var(--primary-color);
}

.translation-item.active {
  background: rgba(79, 70, 229, 0.05);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.item-key {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: var(--primary-color);
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
}

.item-status {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  font-weight: 500;
  text-transform: uppercase;
}

.item-status.untranslated {
  background: rgba(156, 163, 175, 0.1);
  color: var(--text-secondary);
}

.item-status.translated {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.item-status.reviewed {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.item-status.approved {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.item-source {
  font-size: 14px;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: 1.4;
}

.item-target {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
  font-style: italic;
}

/* 翻译编辑器 */
.translation-editor {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.editor-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.editor-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.editor-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
}

.current-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.item-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.item-context {
  font-size: 12px;
  color: var(--text-tertiary);
}

.section-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.source-text {
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
}

.translation-input {
  width: 100%;
  min-height: 100px;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  font-family: inherit;
}

.translation-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.input-tools {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
}

.tool-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
  border-radius: var(--border-radius-sm);
  font-size: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.tool-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* 翻译建议 */
.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.suggestion-item {
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-md);
}

.suggestion-text {
  flex: 1;
  font-size: 14px;
  color: var(--text-primary);
}

.suggestion-source {
  font-size: 12px;
  color: var(--text-tertiary);
  padding: 2px 6px;
  background: var(--bg-tertiary);
  border-radius: var(--border-radius-sm);
}

/* 术语词典 */
.glossary-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.glossary-item {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-secondary);
  align-items: center;
}

.term-source {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
}

.term-target {
  font-size: 14px;
  color: var(--text-secondary);
}

.term-category {
  font-size: 11px;
  color: var(--text-tertiary);
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  text-align: center;
}

/* 编辑器底部 */
.editor-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-color);
}

.translation-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.translation-status label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.editor-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* 工作台底部 */
.workbench-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-top: var(--spacing-lg);
}

.progress-stats {
  display: flex;
  gap: var(--spacing-lg);
}

.stat-item {
  display: flex;
  gap: var(--spacing-xs);
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.stat-value {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.auto-save-status {
  font-size: 12px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* 语言包管理样式 */
.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.language-packs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-lg);
}

.language-pack-card {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  transition: all 0.2s ease;
}

.language-pack-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.pack-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
}

.pack-language {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.language-flag {
  font-size: 24px;
}

.language-info h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.language-info p {
  margin: 0;
  font-size: 12px;
  color: var(--text-secondary);
}

.pack-status {
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.pack-status.published {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.pack-status.draft {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.pack-status.archived {
  background: rgba(156, 163, 175, 0.1);
  color: var(--text-secondary);
}

.pack-stats {
  margin-bottom: var(--spacing-md);
}

.stat-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
  font-size: 14px;
}

.stat-row:last-child {
  margin-bottom: 0;
}

.stat-row .stat-label {
  color: var(--text-secondary);
}

.stat-row .stat-value {
  color: var(--text-primary);
  font-weight: 500;
}

.pack-progress {
  margin-bottom: var(--spacing-md);
}

.progress-bar-container {
  height: 6px;
  background: var(--bg-tertiary);
  border-radius: 3px;
  overflow: hidden;
}

.pack-actions {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

/* 版本时间线 */
.version-timeline {
  position: relative;
  padding-left: var(--spacing-lg);
}

.version-timeline::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--border-color);
}

.version-item {
  position: relative;
  margin-bottom: var(--spacing-xl);
  padding-left: var(--spacing-lg);
}

.version-item:last-child {
  margin-bottom: 0;
}

.version-marker {
  position: absolute;
  left: -8px;
  top: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid var(--bg-primary);
}

.version-marker.published {
  background: var(--success-color);
}

.version-marker.draft {
  background: var(--warning-color);
}

.version-marker.archived {
  background: var(--text-secondary);
}

.version-content {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
}

.version-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.version-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.version-status {
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
}

.version-status.published {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.version-status.archived {
  background: rgba(156, 163, 175, 0.1);
  color: var(--text-secondary);
}

.version-date {
  font-size: 12px;
  color: var(--text-tertiary);
  margin-left: auto;
}

.version-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
  line-height: 1.4;
}

.version-stats {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.version-stats .stat-item {
  font-size: 12px;
  color: var(--text-tertiary);
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
}

.version-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 上传区域 */
.upload-area {
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-2xl);
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: var(--spacing-lg);
}

.upload-area:hover,
.upload-area.dragover {
  border-color: var(--primary-color);
  background: rgba(79, 70, 229, 0.05);
}

.upload-icon {
  font-size: 48px;
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-md);
}

.upload-text p {
  margin: 0;
  color: var(--text-secondary);
}

.upload-hint {
  font-size: 12px;
  color: var(--text-tertiary);
}

.import-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

/* 质量控制样式 */
.quality-toolbar {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
  align-items: end;
}

.toolbar-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.toolbar-section label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
}

/* 审核列表 */
.review-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.review-item {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-primary);
  overflow: hidden;
}

.review-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.review-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
}

.review-key {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: var(--primary-color);
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
}

.review-language {
  font-size: 14px;
  font-weight: 500;
}

.review-status {
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.review-status.pending {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.review-status.approved {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.review-status.rejected {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.review-status.needs-revision {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.review-content {
  padding: var(--spacing-lg);
}

.source-text,
.target-text {
  margin-bottom: var(--spacing-md);
}

.source-text label,
.target-text label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.source-text p,
.target-text p {
  margin: 0;
  padding: var(--spacing-sm);
  background: var(--bg-tertiary);
  border-radius: var(--border-radius);
  line-height: 1.5;
}

.quality-issues {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.issue-tag {
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.issue-tag.grammar {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.issue-tag.terminology {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.issue-tag.context {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.issue-tag.formatting {
  background: rgba(156, 163, 175, 0.1);
  color: var(--text-secondary);
}

.issue-description {
  font-size: 13px;
  color: var(--text-secondary);
  font-style: italic;
  margin-top: var(--spacing-xs);
}

.quality-score {
  margin-bottom: var(--spacing-md);
}

.score-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.score-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.score-value {
  font-size: 16px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
}

.score-value.excellent {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.score-value.good {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.score-value.average {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.score-value.poor {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.revision-history {
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-color);
}

.revision-item {
  padding: var(--spacing-sm);
  background: var(--bg-tertiary);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-sm);
}

.revision-author,
.revision-time {
  font-size: 12px;
  color: var(--text-tertiary);
  margin-right: var(--spacing-sm);
}

.revision-comment {
  margin: var(--spacing-xs) 0 0 0;
  font-size: 13px;
  color: var(--text-secondary);
}

.review-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
}

.reviewer-info {
  display: flex;
  gap: var(--spacing-md);
  font-size: 12px;
  color: var(--text-secondary);
}

.review-buttons {
  display: flex;
  gap: var(--spacing-xs);
}

/* 质量分析 */
.quality-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
}

.metric-item {
  padding: var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-secondary);
}

.metric-header h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.language-quality-item {
  display: grid;
  grid-template-columns: 100px 1fr 60px;
  gap: var(--spacing-sm);
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.language-name {
  font-size: 14px;
  font-weight: 500;
}

.quality-bar {
  height: 8px;
  background: var(--bg-tertiary);
  border-radius: 4px;
  overflow: hidden;
}

.quality-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.quality-fill.excellent {
  background: linear-gradient(to right, var(--success-color), #10B981);
}

.quality-fill.good {
  background: linear-gradient(to right, var(--info-color), #3B82F6);
}

.quality-fill.average {
  background: linear-gradient(to right, var(--warning-color), #F59E0B);
}

.quality-score {
  font-size: 14px;
  font-weight: 500;
  text-align: right;
}

.issue-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.issue-stat-item {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: var(--spacing-sm);
  align-items: center;
  padding: var(--spacing-sm);
  background: var(--bg-tertiary);
  border-radius: var(--border-radius);
}

.issue-type {
  font-size: 14px;
  color: var(--text-primary);
}

.issue-count {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.issue-percentage {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 审核详情模态框 */
.review-detail {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.detail-section h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--spacing-sm);
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.detail-item label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
}

.detail-item span {
  font-size: 14px;
  color: var(--text-primary);
}

.translation-comparison {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.comparison-item label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.text-content {
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--border-radius);
  line-height: 1.5;
}

.quality-assessment {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-md);
}

.assessment-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  text-align: center;
}

.assessment-item label {
  font-size: 12px;
  color: var(--text-secondary);
}

.assessment-item .score {
  font-size: 18px;
  font-weight: 600;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
}

/* 多语言内容管理样式 */
.content-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: end;
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
  gap: var(--spacing-md);
}

.toolbar-left {
  display: flex;
  gap: var(--spacing-md);
  flex: 1;
}

.toolbar-right {
  display: flex;
  gap: var(--spacing-sm);
}

.search-section,
.filter-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.search-section {
  flex: 2;
}

.filter-section {
  flex: 1;
}

/* 内容标签 */
.content-tags {
  margin-top: var(--spacing-xs);
}

.tag {
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
}

.tag.ui {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.tag.error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.tag.notification {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.tag.help {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.tag.marketing {
  background: rgba(139, 92, 246, 0.1);
  color: #8B5CF6;
}

/* 进度指示器 */
.progress-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.progress-bar-mini {
  width: 60px;
  height: 4px;
  background: var(--bg-tertiary);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(to right, var(--primary-color), var(--primary-light));
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: var(--text-secondary);
  white-space: nowrap;
}

/* 源文本样式 */
.source-text {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}

/* 视图切换 */
.view-toggle {
  display: flex;
  gap: 0;
}

.view-toggle .btn {
  border-radius: 0;
  border-right: none;
}

.view-toggle .btn:first-child {
  border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.view-toggle .btn:last-child {
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
  border-right: 1px solid var(--border-color);
}

.view-toggle .btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* 卡片视图 */
.content-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--spacing-lg);
}

.content-card {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-primary);
  overflow: hidden;
  transition: all 0.2s ease;
}

.content-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.content-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.content-card .card-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.content-card .card-title code {
  margin-right: var(--spacing-sm);
}

.content-card .card-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.content-card .card-content {
  padding: var(--spacing-md);
}

.content-card .source-text {
  font-size: 14px;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  max-width: none;
  white-space: normal;
  line-height: 1.5;
}

.translation-preview {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.translation-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--bg-tertiary);
  border-radius: var(--border-radius-sm);
  font-size: 13px;
}

.translation-item.missing {
  opacity: 0.6;
}

.translation-item.missing .translation-text {
  color: var(--text-tertiary);
  font-style: italic;
}

.lang-flag {
  font-size: 16px;
  flex-shrink: 0;
}

.translation-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content-card .card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
}

.last-updated {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 内容详情模态框 */
.content-detail {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.source-content {
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
}

.source-content p {
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
  color: var(--text-primary);
}

.translations-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.translation-row {
  display: grid;
  grid-template-columns: 120px 1fr auto auto;
  gap: var(--spacing-md);
  align-items: center;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-secondary);
}

.translation-lang {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.lang-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.translation-content {
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.4;
}

.translation-status {
  display: flex;
  justify-content: center;
}

.translation-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .content-toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-left {
    flex-direction: column;
  }

  .content-cards-grid {
    grid-template-columns: 1fr;
  }

  .translation-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .translation-lang {
    justify-content: space-between;
  }
}

/* 首页样式 */
.welcome-section {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: var(--spacing-2xl);
  align-items: center;
  margin-bottom: var(--spacing-2xl);
  padding: var(--spacing-2xl);
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.05), rgba(16, 185, 129, 0.05));
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
}

.welcome-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.welcome-title i {
  color: var(--primary-color);
}

.welcome-description {
  font-size: 18px;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

.feature-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xl) 0;
  text-align: center;
}

/* 功能模块网格 */
.modules-section {
  margin-bottom: var(--spacing-2xl);
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
}

.module-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.module-card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
  transform: translateY(-4px);
}

.module-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-lg);
}

.module-icon i {
  font-size: 28px;
  color: white;
}

.module-content {
  flex: 1;
  margin-bottom: var(--spacing-lg);
}

.module-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.module-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0 0 var(--spacing-md) 0;
}

.module-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.feature-tag {
  padding: 4px 8px;
  background: var(--bg-tertiary);
  border-radius: var(--border-radius-sm);
  font-size: 12px;
  color: var(--text-secondary);
}

.module-actions {
  margin-top: auto;
}

/* 快速开始 */
.quick-start-section {
  margin-bottom: var(--spacing-2xl);
}

.quick-start-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.quick-start-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
}

.quick-start-item:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.step-number {
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 18px;
  flex-shrink: 0;
}

.step-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.step-content p {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.4;
  margin: 0 0 var(--spacing-sm) 0;
}

.step-link {
  font-size: 14px;
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.step-link:hover {
  color: var(--primary-dark);
}

/* 系统特性 */
.features-section {
  margin-bottom: var(--spacing-2xl);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
}

.feature-item {
  text-align: center;
  padding: var(--spacing-xl);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
}

.feature-item:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.feature-icon {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, var(--success-color), #10B981);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-md);
}

.feature-icon i {
  font-size: 24px;
  color: white;
}

.feature-item h4 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.feature-item p {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .welcome-section {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .welcome-title {
    font-size: 24px;
  }

  .welcome-description {
    font-size: 16px;
  }

  .modules-grid {
    grid-template-columns: 1fr;
  }

  .quick-start-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .features-grid {
    grid-template-columns: 1fr;
  }
}

/* 响应式 */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }
}
