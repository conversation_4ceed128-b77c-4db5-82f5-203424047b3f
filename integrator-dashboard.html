<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>集成商后台 - 仪表板</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/common.css">
</head>
<body data-user-role="integrator">
  <div class="layout">
    <!-- 侧边栏 -->
    <div id="sidebar-container"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 头部 -->
      <div id="header-container"></div>

      <!-- 内容区域 -->
      <main class="content">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">
            <i class="fas fa-tachometer-alt"></i>
            集成商仪表板
          </h1>
          <p class="page-description">项目概览和翻译进度监控</p>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-project-diagram text-primary"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">23</div>
              <div class="stat-label">总项目数</div>
              <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                +3 本月
              </div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-tasks text-success"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">18</div>
              <div class="stat-label">进行中项目</div>
              <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                +2 本周
              </div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-check-circle text-info"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">87.5%</div>
              <div class="stat-label">平均完成率</div>
              <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                +5.2% 本月
              </div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-users text-warning"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">12</div>
              <div class="stat-label">团队成员</div>
              <div class="stat-change neutral">
                <i class="fas fa-minus"></i>
                无变化
              </div>
            </div>
          </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="dashboard-grid">
          <!-- 项目进度概览 -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">项目进度概览</h3>
              <a href="integrator-projects.html" class="btn btn-sm btn-secondary">查看全部</a>
            </div>
            <div class="card-body">
              <div class="project-list">
                <div class="project-item">
                  <div class="project-info">
                    <div class="project-name">电商平台多语言</div>
                    <div class="project-meta">
                      <span class="project-languages">5种语言</span>
                      <span class="project-deadline">截止: 2024-04-15</span>
                    </div>
                  </div>
                  <div class="project-progress">
                    <div class="progress-info">
                      <span class="progress-text">85%</span>
                    </div>
                    <div class="progress-bar-container">
                      <div class="progress-bar" style="width: 85%"></div>
                    </div>
                  </div>
                </div>

                <div class="project-item">
                  <div class="project-info">
                    <div class="project-name">移动应用国际化</div>
                    <div class="project-meta">
                      <span class="project-languages">8种语言</span>
                      <span class="project-deadline">截止: 2024-04-20</span>
                    </div>
                  </div>
                  <div class="project-progress">
                    <div class="progress-info">
                      <span class="progress-text">62%</span>
                    </div>
                    <div class="progress-bar-container">
                      <div class="progress-bar" style="width: 62%"></div>
                    </div>
                  </div>
                </div>

                <div class="project-item">
                  <div class="project-info">
                    <div class="project-name">企业官网翻译</div>
                    <div class="project-meta">
                      <span class="project-languages">3种语言</span>
                      <span class="project-deadline">截止: 2024-04-25</span>
                    </div>
                  </div>
                  <div class="project-progress">
                    <div class="progress-info">
                      <span class="progress-text">95%</span>
                    </div>
                    <div class="progress-bar-container">
                      <div class="progress-bar" style="width: 95%"></div>
                    </div>
                  </div>
                </div>

                <div class="project-item">
                  <div class="project-info">
                    <div class="project-name">产品文档本地化</div>
                    <div class="project-meta">
                      <span class="project-languages">6种语言</span>
                      <span class="project-deadline">截止: 2024-05-01</span>
                    </div>
                  </div>
                  <div class="project-progress">
                    <div class="progress-info">
                      <span class="progress-text">38%</span>
                    </div>
                    <div class="progress-bar-container">
                      <div class="progress-bar" style="width: 38%"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 语言分布 -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">语言分布统计</h3>
            </div>
            <div class="card-body">
              <div class="language-stats">
                <div class="language-item">
                  <div class="language-info">
                    <span class="language-flag">🇺🇸</span>
                    <span class="language-name">英语</span>
                    <span class="language-count">23 项目</span>
                  </div>
                  <div class="language-progress">
                    <div class="progress-bar" style="width: 100%"></div>
                  </div>
                </div>
                <div class="language-item">
                  <div class="language-info">
                    <span class="language-flag">🇯🇵</span>
                    <span class="language-name">日语</span>
                    <span class="language-count">18 项目</span>
                  </div>
                  <div class="language-progress">
                    <div class="progress-bar" style="width: 78%"></div>
                  </div>
                </div>
                <div class="language-item">
                  <div class="language-info">
                    <span class="language-flag">🇰🇷</span>
                    <span class="language-name">韩语</span>
                    <span class="language-count">15 项目</span>
                  </div>
                  <div class="language-progress">
                    <div class="progress-bar" style="width: 65%"></div>
                  </div>
                </div>
                <div class="language-item">
                  <div class="language-info">
                    <span class="language-flag">🇫🇷</span>
                    <span class="language-name">法语</span>
                    <span class="language-count">12 项目</span>
                  </div>
                  <div class="language-progress">
                    <div class="progress-bar" style="width: 52%"></div>
                  </div>
                </div>
                <div class="language-item">
                  <div class="language-info">
                    <span class="language-flag">🇩🇪</span>
                    <span class="language-name">德语</span>
                    <span class="language-count">8 项目</span>
                  </div>
                  <div class="language-progress">
                    <div class="progress-bar" style="width: 35%"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 团队活动 -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">团队活动</h3>
              <a href="integrator-team.html" class="btn btn-sm btn-secondary">管理团队</a>
            </div>
            <div class="card-body">
              <div class="activity-list">
                <div class="activity-item">
                  <div class="activity-avatar">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face" 
                         alt="用户头像" class="avatar">
                  </div>
                  <div class="activity-content">
                    <div class="activity-title">李小明 完成了英文翻译</div>
                    <div class="activity-description">电商平台项目 - 产品页面</div>
                    <div class="activity-time">10分钟前</div>
                  </div>
                </div>
                <div class="activity-item">
                  <div class="activity-avatar">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face" 
                         alt="用户头像" class="avatar">
                  </div>
                  <div class="activity-content">
                    <div class="activity-title">王大华 提交了审核</div>
                    <div class="activity-description">移动应用项目 - 日文翻译</div>
                    <div class="activity-time">1小时前</div>
                  </div>
                </div>
                <div class="activity-item">
                  <div class="activity-avatar">
                    <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face" 
                         alt="用户头像" class="avatar">
                  </div>
                  <div class="activity-content">
                    <div class="activity-title">张美丽 开始了新任务</div>
                    <div class="activity-description">企业官网项目 - 韩文翻译</div>
                    <div class="activity-time">2小时前</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 快速操作 -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">快速操作</h3>
            </div>
            <div class="card-body">
              <div class="quick-actions">
                <button class="quick-action-btn" onclick="createProject()">
                  <i class="fas fa-plus"></i>
                  <span>新建项目</span>
                </button>
                <button class="quick-action-btn" onclick="uploadFiles()">
                  <i class="fas fa-upload"></i>
                  <span>上传文件</span>
                </button>
                <button class="quick-action-btn" onclick="inviteMembers()">
                  <i class="fas fa-user-plus"></i>
                  <span>邀请成员</span>
                </button>
                <button class="quick-action-btn" onclick="exportData()">
                  <i class="fas fa-download"></i>
                  <span>导出数据</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <script src="assets/js/common.js"></script>
  <script>
    // 加载组件
    document.addEventListener('DOMContentLoaded', () => {
      fetch('components/sidebar.html')
        .then(response => response.text())
        .then(html => {
          document.getElementById('sidebar-container').innerHTML = html;
        });

      fetch('components/header.html')
        .then(response => response.text())
        .then(html => {
          document.getElementById('header-container').innerHTML = html;
          updateBreadcrumb();
        });
    });

    function updateBreadcrumb() {
      const breadcrumb = document.querySelector('.breadcrumb-current');
      if (breadcrumb) {
        breadcrumb.textContent = '集成商仪表板';
      }
    }

    // 快速操作
    function createProject() {
      window.location.href = 'integrator-projects.html?action=create';
    }

    function uploadFiles() {
      console.log('上传文件');
    }

    function inviteMembers() {
      window.location.href = 'integrator-team.html?action=invite';
    }

    function exportData() {
      console.log('导出数据');
    }
  </script>
</body>
</html>
