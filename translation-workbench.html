<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>多语言翻译工作台</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/common.css">
</head>
<body data-user-role="project">
  <div class="layout">
    <!-- 侧边栏 -->
    <div id="sidebar-container"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 头部 -->
      <div id="header-container"></div>

      <!-- 内容区域 -->
      <main class="content">
        <!-- 工作台头部 -->
        <div class="workbench-header">
          <div class="project-info">
            <h1 class="project-title">
              <i class="fas fa-language"></i>
              电商平台多语言项目
            </h1>
            <div class="project-meta">
              <span class="project-status active">进行中</span>
              <span class="project-progress">完成度: 68%</span>
              <span class="project-deadline">截止: 2024-04-15</span>
            </div>
          </div>
          <div class="workbench-actions">
            <button class="btn btn-secondary" onclick="saveProgress()">
              <i class="fas fa-save"></i>
              保存进度
            </button>
            <button class="btn btn-primary" onclick="submitForReview()">
              <i class="fas fa-check"></i>
              提交审核
            </button>
          </div>
        </div>

        <!-- 语言选择和过滤 -->
        <div class="language-selector-bar">
          <div class="source-language">
            <label>源语言:</label>
            <select class="form-control" id="sourceLanguage">
              <option value="zh-CN">🇨🇳 简体中文</option>
              <option value="en-US">🇺🇸 英语</option>
            </select>
          </div>
          <div class="target-language">
            <label>目标语言:</label>
            <select class="form-control" id="targetLanguage">
              <option value="en-US">🇺🇸 英语</option>
              <option value="ja-JP">🇯🇵 日语</option>
              <option value="ko-KR">🇰🇷 韩语</option>
              <option value="fr-FR">🇫🇷 法语</option>
              <option value="de-DE">🇩🇪 德语</option>
            </select>
          </div>
          <div class="filter-options">
            <select class="form-control" id="statusFilter">
              <option value="">全部状态</option>
              <option value="untranslated">未翻译</option>
              <option value="translated">已翻译</option>
              <option value="reviewed">已审核</option>
              <option value="approved">已批准</option>
            </select>
          </div>
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" class="form-control" placeholder="搜索文本内容..." id="searchText">
          </div>
        </div>

        <!-- 翻译工作区 -->
        <div class="translation-workspace">
          <!-- 左侧：翻译列表 -->
          <div class="translation-list">
            <div class="list-header">
              <h3>翻译条目 <span class="item-count">(245)</span></h3>
              <div class="list-actions">
                <button class="btn btn-sm btn-secondary" onclick="exportTranslations()">
                  <i class="fas fa-download"></i>
                </button>
                <button class="btn btn-sm btn-secondary" onclick="importTranslations()">
                  <i class="fas fa-upload"></i>
                </button>
              </div>
            </div>
            
            <div class="translation-items">
              <div class="translation-item active" data-key="welcome_message">
                <div class="item-header">
                  <span class="item-key">welcome_message</span>
                  <span class="item-status untranslated">未翻译</span>
                </div>
                <div class="item-source">欢迎来到我们的电商平台！</div>
                <div class="item-target">Welcome to our e-commerce platform!</div>
              </div>

              <div class="translation-item" data-key="product_title">
                <div class="item-header">
                  <span class="item-key">product_title</span>
                  <span class="item-status translated">已翻译</span>
                </div>
                <div class="item-source">商品详情</div>
                <div class="item-target">Product Details</div>
              </div>

              <div class="translation-item" data-key="add_to_cart">
                <div class="item-header">
                  <span class="item-key">add_to_cart</span>
                  <span class="item-status reviewed">已审核</span>
                </div>
                <div class="item-source">加入购物车</div>
                <div class="item-target">Add to Cart</div>
              </div>

              <div class="translation-item" data-key="checkout_button">
                <div class="item-header">
                  <span class="item-key">checkout_button</span>
                  <span class="item-status approved">已批准</span>
                </div>
                <div class="item-source">立即结账</div>
                <div class="item-target">Checkout Now</div>
              </div>

              <div class="translation-item" data-key="user_profile">
                <div class="item-header">
                  <span class="item-key">user_profile</span>
                  <span class="item-status untranslated">未翻译</span>
                </div>
                <div class="item-source">用户资料</div>
                <div class="item-target"></div>
              </div>
            </div>
          </div>

          <!-- 右侧：翻译编辑器 -->
          <div class="translation-editor">
            <div class="editor-header">
              <h3>翻译编辑器</h3>
              <div class="editor-actions">
                <button class="btn btn-sm btn-secondary" onclick="useAITranslation()">
                  <i class="fas fa-robot"></i>
                  AI翻译
                </button>
                <button class="btn btn-sm btn-secondary" onclick="showTranslationMemory()">
                  <i class="fas fa-memory"></i>
                  翻译记忆
                </button>
              </div>
            </div>

            <div class="editor-content">
              <!-- 当前编辑项 -->
              <div class="current-item">
                <div class="item-info">
                  <span class="item-key">welcome_message</span>
                  <span class="item-context">首页 > 欢迎信息</span>
                </div>
                
                <!-- 源文本 -->
                <div class="source-section">
                  <label class="section-label">源文本 (简体中文)</label>
                  <div class="source-text">
                    欢迎来到我们的电商平台！
                  </div>
                </div>

                <!-- 翻译文本 -->
                <div class="target-section">
                  <label class="section-label">翻译文本 (英语)</label>
                  <textarea class="translation-input" placeholder="请输入翻译内容...">Welcome to our e-commerce platform!</textarea>
                  <div class="input-tools">
                    <button class="tool-btn" onclick="insertVariable()">
                      <i class="fas fa-code"></i>
                      变量
                    </button>
                    <button class="tool-btn" onclick="checkSpelling()">
                      <i class="fas fa-spell-check"></i>
                      拼写
                    </button>
                    <button class="tool-btn" onclick="formatText()">
                      <i class="fas fa-text-width"></i>
                      格式
                    </button>
                  </div>
                </div>

                <!-- 翻译建议 -->
                <div class="suggestions-section">
                  <label class="section-label">翻译建议</label>
                  <div class="suggestions-list">
                    <div class="suggestion-item">
                      <div class="suggestion-text">Welcome to our e-commerce platform!</div>
                      <div class="suggestion-source">AI翻译</div>
                      <button class="btn btn-sm btn-secondary" onclick="applySuggestion(this)">应用</button>
                    </div>
                    <div class="suggestion-item">
                      <div class="suggestion-text">Welcome to our online shopping platform!</div>
                      <div class="suggestion-source">翻译记忆</div>
                      <button class="btn btn-sm btn-secondary" onclick="applySuggestion(this)">应用</button>
                    </div>
                  </div>
                </div>

                <!-- 术语词典 -->
                <div class="glossary-section">
                  <label class="section-label">相关术语</label>
                  <div class="glossary-list">
                    <div class="glossary-item">
                      <span class="term-source">电商平台</span>
                      <span class="term-target">e-commerce platform</span>
                      <span class="term-category">商业</span>
                    </div>
                    <div class="glossary-item">
                      <span class="term-source">欢迎</span>
                      <span class="term-target">welcome</span>
                      <span class="term-category">通用</span>
                    </div>
                  </div>
                </div>

                <!-- 编辑器底部操作 -->
                <div class="editor-footer">
                  <div class="translation-status">
                    <label>状态:</label>
                    <select class="form-control form-control-sm">
                      <option value="untranslated">未翻译</option>
                      <option value="translated" selected>已翻译</option>
                      <option value="reviewed">已审核</option>
                      <option value="approved">已批准</option>
                    </select>
                  </div>
                  <div class="editor-actions">
                    <button class="btn btn-secondary" onclick="previousItem()">
                      <i class="fas fa-chevron-left"></i>
                      上一个
                    </button>
                    <button class="btn btn-primary" onclick="saveAndNext()">
                      保存并下一个
                      <i class="fas fa-chevron-right"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部统计栏 -->
        <div class="workbench-footer">
          <div class="progress-stats">
            <div class="stat-item">
              <span class="stat-label">总条目:</span>
              <span class="stat-value">245</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">已翻译:</span>
              <span class="stat-value">167</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">已审核:</span>
              <span class="stat-value">89</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">完成率:</span>
              <span class="stat-value">68%</span>
            </div>
          </div>
          <div class="auto-save-status">
            <i class="fas fa-check-circle text-success"></i>
            自动保存于 14:32
          </div>
        </div>
      </main>
    </div>
  </div>

  <script src="assets/js/common.js"></script>
  <script>
    // 加载组件
    document.addEventListener('DOMContentLoaded', () => {
      fetch('components/sidebar.html')
        .then(response => response.text())
        .then(html => {
          document.getElementById('sidebar-container').innerHTML = html;
        });

      fetch('components/header.html')
        .then(response => response.text())
        .then(html => {
          document.getElementById('header-container').innerHTML = html;
          updateBreadcrumb();
        });

      // 绑定事件
      bindTranslationEvents();
    });

    function updateBreadcrumb() {
      const breadcrumb = document.querySelector('.breadcrumb-current');
      if (breadcrumb) {
        breadcrumb.textContent = '翻译工作台';
      }
    }

    // 绑定翻译相关事件
    function bindTranslationEvents() {
      // 翻译项点击事件
      document.querySelectorAll('.translation-item').forEach(item => {
        item.addEventListener('click', () => {
          document.querySelectorAll('.translation-item').forEach(i => i.classList.remove('active'));
          item.classList.add('active');
          loadTranslationItem(item.dataset.key);
        });
      });

      // 自动保存
      const translationInput = document.querySelector('.translation-input');
      if (translationInput) {
        translationInput.addEventListener('input', debounce(autoSave, 2000));
      }
    }

    // 加载翻译项
    function loadTranslationItem(key) {
      console.log('加载翻译项:', key);
      // 这里应该从服务器加载具体的翻译数据
    }

    // 翻译工作台功能
    function saveProgress() {
      Utils.showNotification('翻译进度已保存', 'success');
    }

    function submitForReview() {
      Utils.confirm('确定要提交当前翻译进行审核吗？', () => {
        Utils.showNotification('已提交审核', 'success');
      });
    }

    function useAITranslation() {
      const input = document.querySelector('.translation-input');
      if (input) {
        input.value = 'Welcome to our e-commerce platform!';
        Utils.showNotification('AI翻译已应用', 'info');
      }
    }

    function showTranslationMemory() {
      console.log('显示翻译记忆');
    }

    function applySuggestion(btn) {
      const suggestionText = btn.parentElement.querySelector('.suggestion-text').textContent;
      const input = document.querySelector('.translation-input');
      if (input) {
        input.value = suggestionText;
        Utils.showNotification('翻译建议已应用', 'success');
      }
    }

    function saveAndNext() {
      saveProgress();
      // 切换到下一个翻译项
      const activeItem = document.querySelector('.translation-item.active');
      const nextItem = activeItem.nextElementSibling;
      if (nextItem) {
        activeItem.classList.remove('active');
        nextItem.classList.add('active');
        loadTranslationItem(nextItem.dataset.key);
      }
    }

    function previousItem() {
      const activeItem = document.querySelector('.translation-item.active');
      const prevItem = activeItem.previousElementSibling;
      if (prevItem) {
        activeItem.classList.remove('active');
        prevItem.classList.add('active');
        loadTranslationItem(prevItem.dataset.key);
      }
    }

    // 自动保存
    function autoSave() {
      const status = document.querySelector('.auto-save-status');
      if (status) {
        const now = new Date();
        status.innerHTML = `<i class="fas fa-check-circle text-success"></i> 自动保存于 ${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;
      }
    }

    // 防抖函数
    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }
  </script>
</body>
</html>
