<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>运营后台 - 集成商管理</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/common.css">
</head>
<body data-user-role="admin">
  <div class="layout">
    <!-- 侧边栏 -->
    <div id="sidebar-container"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 头部 -->
      <div id="header-container"></div>

      <!-- 内容区域 -->
      <main class="content">
        <!-- 页面标题 -->
        <div class="page-header">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="page-title">
                <i class="fas fa-users"></i>
                集成商管理
              </h1>
              <p class="page-description">管理所有集成商账户和权限</p>
            </div>
            <button class="btn btn-primary" onclick="openCreateModal()">
              <i class="fas fa-plus"></i>
              新建集成商
            </button>
          </div>
        </div>

        <!-- 筛选和搜索 -->
        <div class="card mb-4">
          <div class="card-body">
            <div class="filter-row">
              <div class="filter-group">
                <label class="form-label">搜索</label>
                <div class="search-box">
                  <i class="fas fa-search"></i>
                  <input type="text" class="form-control table-filter" placeholder="搜索集成商名称、邮箱...">
                </div>
              </div>
              <div class="filter-group">
                <label class="form-label">状态</label>
                <select class="form-control">
                  <option value="">全部状态</option>
                  <option value="active">活跃</option>
                  <option value="inactive">未激活</option>
                  <option value="suspended">已暂停</option>
                </select>
              </div>
              <div class="filter-group">
                <label class="form-label">注册时间</label>
                <select class="form-control">
                  <option value="">全部时间</option>
                  <option value="today">今天</option>
                  <option value="week">本周</option>
                  <option value="month">本月</option>
                  <option value="quarter">本季度</option>
                </select>
              </div>
              <div class="filter-group">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-secondary">
                  <i class="fas fa-filter"></i>
                  筛选
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 集成商列表 -->
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">集成商列表</h3>
            <div class="card-actions">
              <button class="btn btn-sm btn-secondary">
                <i class="fas fa-download"></i>
                导出
              </button>
            </div>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table">
                <thead>
                  <tr>
                    <th class="sortable">
                      <input type="checkbox" class="select-all">
                    </th>
                    <th class="sortable">集成商名称 <i class="fas fa-sort"></i></th>
                    <th class="sortable">联系人 <i class="fas fa-sort"></i></th>
                    <th class="sortable">邮箱 <i class="fas fa-sort"></i></th>
                    <th class="sortable">项目数量 <i class="fas fa-sort"></i></th>
                    <th class="sortable">状态 <i class="fas fa-sort"></i></th>
                    <th class="sortable">注册时间 <i class="fas fa-sort"></i></th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><input type="checkbox" class="row-select"></td>
                    <td>
                      <div class="company-info">
                        <img src="https://images.unsplash.com/photo-1560179707-f14e90ef3623?w=32&h=32&fit=crop" 
                             alt="公司logo" class="company-logo">
                        <div>
                          <div class="company-name">阿里巴巴集团</div>
                          <div class="company-code">ALI001</div>
                        </div>
                      </div>
                    </td>
                    <td>张三</td>
                    <td><EMAIL></td>
                    <td><span class="badge badge-info">23</span></td>
                    <td><span class="status-badge active">活跃</span></td>
                    <td>2024-01-15</td>
                    <td>
                      <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary" onclick="viewIntegrator(1)">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editIntegrator(1)">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteIntegrator(1)">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td><input type="checkbox" class="row-select"></td>
                    <td>
                      <div class="company-info">
                        <img src="https://images.unsplash.com/photo-1549923746-c502d488b3ea?w=32&h=32&fit=crop" 
                             alt="公司logo" class="company-logo">
                        <div>
                          <div class="company-name">腾讯科技</div>
                          <div class="company-code">TEN002</div>
                        </div>
                      </div>
                    </td>
                    <td>李四</td>
                    <td><EMAIL></td>
                    <td><span class="badge badge-info">18</span></td>
                    <td><span class="status-badge active">活跃</span></td>
                    <td>2024-01-20</td>
                    <td>
                      <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary" onclick="viewIntegrator(2)">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editIntegrator(2)">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteIntegrator(2)">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td><input type="checkbox" class="row-select"></td>
                    <td>
                      <div class="company-info">
                        <img src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=32&h=32&fit=crop" 
                             alt="公司logo" class="company-logo">
                        <div>
                          <div class="company-name">字节跳动</div>
                          <div class="company-code">BYT003</div>
                        </div>
                      </div>
                    </td>
                    <td>王五</td>
                    <td><EMAIL></td>
                    <td><span class="badge badge-info">31</span></td>
                    <td><span class="status-badge inactive">未激活</span></td>
                    <td>2024-02-01</td>
                    <td>
                      <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary" onclick="viewIntegrator(3)">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editIntegrator(3)">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteIntegrator(3)">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td><input type="checkbox" class="row-select"></td>
                    <td>
                      <div class="company-info">
                        <img src="https://images.unsplash.com/photo-1497366216548-37526070297c?w=32&h=32&fit=crop" 
                             alt="公司logo" class="company-logo">
                        <div>
                          <div class="company-name">美团</div>
                          <div class="company-code">MEI004</div>
                        </div>
                      </div>
                    </td>
                    <td>赵六</td>
                    <td><EMAIL></td>
                    <td><span class="badge badge-info">12</span></td>
                    <td><span class="status-badge suspended">已暂停</span></td>
                    <td>2024-02-10</td>
                    <td>
                      <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary" onclick="viewIntegrator(4)">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editIntegrator(4)">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteIntegrator(4)">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 分页 -->
            <div class="pagination-wrapper">
              <div class="pagination-info">
                显示 1-10 条，共 156 条记录
              </div>
              <div class="pagination">
                <button class="btn btn-sm btn-secondary" disabled>
                  <i class="fas fa-chevron-left"></i>
                </button>
                <button class="btn btn-sm btn-primary">1</button>
                <button class="btn btn-sm btn-secondary">2</button>
                <button class="btn btn-sm btn-secondary">3</button>
                <span class="pagination-dots">...</span>
                <button class="btn btn-sm btn-secondary">16</button>
                <button class="btn btn-sm btn-secondary">
                  <i class="fas fa-chevron-right"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 新建集成商模态框 -->
  <div class="modal-overlay" id="createModal">
    <div class="modal modal-lg">
      <div class="modal-header">
        <h3>新建集成商</h3>
        <button class="modal-close" onclick="closeCreateModal()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <form id="createForm">
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">公司名称 *</label>
              <input type="text" class="form-control" name="companyName" required>
            </div>
            <div class="form-group">
              <label class="form-label">公司代码 *</label>
              <input type="text" class="form-control" name="companyCode" required>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">联系人姓名 *</label>
              <input type="text" class="form-control" name="contactName" required>
            </div>
            <div class="form-group">
              <label class="form-label">联系邮箱 *</label>
              <input type="email" class="form-control" name="email" required>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">联系电话</label>
              <input type="tel" class="form-control" name="phone">
            </div>
            <div class="form-group">
              <label class="form-label">公司规模</label>
              <select class="form-control" name="companySize">
                <option value="">请选择</option>
                <option value="small">小型（1-50人）</option>
                <option value="medium">中型（51-200人）</option>
                <option value="large">大型（201-1000人）</option>
                <option value="enterprise">企业级（1000人以上）</option>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="form-label">公司地址</label>
            <textarea class="form-control" name="address" rows="3"></textarea>
          </div>
          <div class="form-group">
            <label class="form-label">备注</label>
            <textarea class="form-control" name="notes" rows="3"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" onclick="closeCreateModal()">取消</button>
        <button class="btn btn-primary" onclick="createIntegrator()">创建</button>
      </div>
    </div>
  </div>

  <script src="assets/js/common.js"></script>
  <script>
    // 加载组件
    document.addEventListener('DOMContentLoaded', () => {
      // 加载侧边栏
      fetch('components/sidebar.html')
        .then(response => response.text())
        .then(html => {
          document.getElementById('sidebar-container').innerHTML = html;
        });

      // 加载头部
      fetch('components/header.html')
        .then(response => response.text())
        .then(html => {
          document.getElementById('header-container').innerHTML = html;
          updateBreadcrumb();
        });
    });

    function updateBreadcrumb() {
      const breadcrumb = document.querySelector('.breadcrumb-current');
      if (breadcrumb) {
        breadcrumb.textContent = '集成商管理';
      }
    }

    // 模态框操作
    function openCreateModal() {
      document.getElementById('createModal').style.display = 'flex';
    }

    function closeCreateModal() {
      document.getElementById('createModal').style.display = 'none';
      document.getElementById('createForm').reset();
    }

    function createIntegrator() {
      const form = document.getElementById('createForm');
      const formData = new FormData(form);
      
      // 这里应该发送到服务器
      console.log('创建集成商:', Object.fromEntries(formData));
      
      Utils.showNotification('集成商创建成功！', 'success');
      closeCreateModal();
    }

    // 集成商操作
    function viewIntegrator(id) {
      console.log('查看集成商:', id);
    }

    function editIntegrator(id) {
      console.log('编辑集成商:', id);
    }

    function deleteIntegrator(id) {
      Utils.confirm('确定要删除这个集成商吗？', () => {
        console.log('删除集成商:', id);
        Utils.showNotification('集成商删除成功！', 'success');
      });
    }

    // 全选功能
    document.addEventListener('change', (e) => {
      if (e.target.classList.contains('select-all')) {
        const checkboxes = document.querySelectorAll('.row-select');
        checkboxes.forEach(cb => cb.checked = e.target.checked);
      }
    });
  </script>
</body>
</html>
