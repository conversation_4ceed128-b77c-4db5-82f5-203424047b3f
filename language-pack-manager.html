<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>语言包管理</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/common.css">
</head>
<body data-user-role="project">
  <div class="layout">
    <!-- 侧边栏 -->
    <div id="sidebar-container"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 头部 -->
      <div id="header-container"></div>

      <!-- 内容区域 -->
      <main class="content">
        <!-- 页面标题 -->
        <div class="page-header">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="page-title">
                <i class="fas fa-box"></i>
                语言包管理
              </h1>
              <p class="page-description">管理项目的多语言文件和版本</p>
            </div>
            <div class="header-actions">
              <button class="btn btn-secondary" onclick="importLanguagePack()">
                <i class="fas fa-upload"></i>
                导入语言包
              </button>
              <button class="btn btn-primary" onclick="createLanguagePack()">
                <i class="fas fa-plus"></i>
                创建语言包
              </button>
            </div>
          </div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-grid mb-4">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-archive text-primary"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">8</div>
              <div class="stat-label">语言包总数</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-check-circle text-success"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">6</div>
              <div class="stat-label">已发布版本</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-clock text-warning"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">2</div>
              <div class="stat-label">待发布版本</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-download text-info"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">1,245</div>
              <div class="stat-label">总下载次数</div>
            </div>
          </div>
        </div>

        <!-- 语言包列表 -->
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">语言包列表</h3>
            <div class="card-actions">
              <div class="filter-group">
                <select class="form-control form-control-sm">
                  <option value="">全部语言</option>
                  <option value="en-US">英语</option>
                  <option value="ja-JP">日语</option>
                  <option value="ko-KR">韩语</option>
                  <option value="fr-FR">法语</option>
                </select>
              </div>
              <div class="filter-group">
                <select class="form-control form-control-sm">
                  <option value="">全部状态</option>
                  <option value="draft">草稿</option>
                  <option value="published">已发布</option>
                  <option value="archived">已归档</option>
                </select>
              </div>
              <button class="btn btn-sm btn-secondary" onclick="exportAllPacks()">
                <i class="fas fa-download"></i>
                批量导出
              </button>
            </div>
          </div>
          <div class="card-body">
            <div class="language-packs-grid">
              <!-- 英语语言包 -->
              <div class="language-pack-card">
                <div class="pack-header">
                  <div class="pack-language">
                    <span class="language-flag">🇺🇸</span>
                    <div class="language-info">
                      <h4>英语 (en-US)</h4>
                      <p>English</p>
                    </div>
                  </div>
                  <div class="pack-status published">已发布</div>
                </div>
                <div class="pack-stats">
                  <div class="stat-row">
                    <span class="stat-label">当前版本:</span>
                    <span class="stat-value">v2.1.0</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">翻译条目:</span>
                    <span class="stat-value">245 / 245</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">完成度:</span>
                    <span class="stat-value">100%</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">最后更新:</span>
                    <span class="stat-value">2024-03-15</span>
                  </div>
                </div>
                <div class="pack-progress">
                  <div class="progress-bar-container">
                    <div class="progress-bar" style="width: 100%"></div>
                  </div>
                </div>
                <div class="pack-actions">
                  <button class="btn btn-sm btn-secondary" onclick="viewPackDetails('en-US')">
                    <i class="fas fa-eye"></i>
                    查看
                  </button>
                  <button class="btn btn-sm btn-primary" onclick="editPack('en-US')">
                    <i class="fas fa-edit"></i>
                    编辑
                  </button>
                  <button class="btn btn-sm btn-success" onclick="downloadPack('en-US')">
                    <i class="fas fa-download"></i>
                    下载
                  </button>
                </div>
              </div>

              <!-- 日语语言包 -->
              <div class="language-pack-card">
                <div class="pack-header">
                  <div class="pack-language">
                    <span class="language-flag">🇯🇵</span>
                    <div class="language-info">
                      <h4>日语 (ja-JP)</h4>
                      <p>日本語</p>
                    </div>
                  </div>
                  <div class="pack-status published">已发布</div>
                </div>
                <div class="pack-stats">
                  <div class="stat-row">
                    <span class="stat-label">当前版本:</span>
                    <span class="stat-value">v1.8.2</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">翻译条目:</span>
                    <span class="stat-value">238 / 245</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">完成度:</span>
                    <span class="stat-value">97%</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">最后更新:</span>
                    <span class="stat-value">2024-03-12</span>
                  </div>
                </div>
                <div class="pack-progress">
                  <div class="progress-bar-container">
                    <div class="progress-bar" style="width: 97%"></div>
                  </div>
                </div>
                <div class="pack-actions">
                  <button class="btn btn-sm btn-secondary" onclick="viewPackDetails('ja-JP')">
                    <i class="fas fa-eye"></i>
                    查看
                  </button>
                  <button class="btn btn-sm btn-primary" onclick="editPack('ja-JP')">
                    <i class="fas fa-edit"></i>
                    编辑
                  </button>
                  <button class="btn btn-sm btn-success" onclick="downloadPack('ja-JP')">
                    <i class="fas fa-download"></i>
                    下载
                  </button>
                </div>
              </div>

              <!-- 韩语语言包 */
              <div class="language-pack-card">
                <div class="pack-header">
                  <div class="pack-language">
                    <span class="language-flag">🇰🇷</span>
                    <div class="language-info">
                      <h4>韩语 (ko-KR)</h4>
                      <p>한국어</p>
                    </div>
                  </div>
                  <div class="pack-status draft">草稿</div>
                </div>
                <div class="pack-stats">
                  <div class="stat-row">
                    <span class="stat-label">当前版本:</span>
                    <span class="stat-value">v1.0.0-beta</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">翻译条目:</span>
                    <span class="stat-value">156 / 245</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">完成度:</span>
                    <span class="stat-value">64%</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">最后更新:</span>
                    <span class="stat-value">2024-03-10</span>
                  </div>
                </div>
                <div class="pack-progress">
                  <div class="progress-bar-container">
                    <div class="progress-bar" style="width: 64%"></div>
                  </div>
                </div>
                <div class="pack-actions">
                  <button class="btn btn-sm btn-secondary" onclick="viewPackDetails('ko-KR')">
                    <i class="fas fa-eye"></i>
                    查看
                  </button>
                  <button class="btn btn-sm btn-primary" onclick="editPack('ko-KR')">
                    <i class="fas fa-edit"></i>
                    编辑
                  </button>
                  <button class="btn btn-sm btn-warning" onclick="publishPack('ko-KR')">
                    <i class="fas fa-rocket"></i>
                    发布
                  </button>
                </div>
              </div>

              <!-- 法语语言包 */
              <div class="language-pack-card">
                <div class="pack-header">
                  <div class="pack-language">
                    <span class="language-flag">🇫🇷</span>
                    <div class="language-info">
                      <h4>法语 (fr-FR)</h4>
                      <p>Français</p>
                    </div>
                  </div>
                  <div class="pack-status draft">草稿</div>
                </div>
                <div class="pack-stats">
                  <div class="stat-row">
                    <span class="stat-label">当前版本:</span>
                    <span class="stat-value">v0.5.0-alpha</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">翻译条目:</span>
                    <span class="stat-value">89 / 245</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">完成度:</span>
                    <span class="stat-value">36%</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">最后更新:</span>
                    <span class="stat-value">2024-03-08</span>
                  </div>
                </div>
                <div class="pack-progress">
                  <div class="progress-bar-container">
                    <div class="progress-bar" style="width: 36%"></div>
                  </div>
                </div>
                <div class="pack-actions">
                  <button class="btn btn-sm btn-secondary" onclick="viewPackDetails('fr-FR')">
                    <i class="fas fa-eye"></i>
                    查看
                  </button>
                  <button class="btn btn-sm btn-primary" onclick="editPack('fr-FR')">
                    <i class="fas fa-edit"></i>
                    编辑
                  </button>
                  <button class="btn btn-sm btn-danger" onclick="deletePack('fr-FR')">
                    <i class="fas fa-trash"></i>
                    删除
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 版本历史 -->
        <div class="card mt-4">
          <div class="card-header">
            <h3 class="card-title">版本历史</h3>
            <div class="card-actions">
              <select class="form-control form-control-sm">
                <option value="en-US">英语 (en-US)</option>
                <option value="ja-JP">日语 (ja-JP)</option>
                <option value="ko-KR">韩语 (ko-KR)</option>
                <option value="fr-FR">法语 (fr-FR)</option>
              </select>
            </div>
          </div>
          <div class="card-body">
            <div class="version-timeline">
              <div class="version-item">
                <div class="version-marker published"></div>
                <div class="version-content">
                  <div class="version-header">
                    <h4>v2.1.0</h4>
                    <span class="version-status published">已发布</span>
                    <span class="version-date">2024-03-15</span>
                  </div>
                  <div class="version-description">
                    修复了购物车页面的翻译问题，新增了用户反馈相关的翻译条目
                  </div>
                  <div class="version-stats">
                    <span class="stat-item">245 条目</span>
                    <span class="stat-item">+12 新增</span>
                    <span class="stat-item">-3 删除</span>
                    <span class="stat-item">~8 修改</span>
                  </div>
                  <div class="version-actions">
                    <button class="btn btn-sm btn-secondary" onclick="viewVersion('v2.1.0')">查看详情</button>
                    <button class="btn btn-sm btn-success" onclick="downloadVersion('v2.1.0')">下载</button>
                  </div>
                </div>
              </div>

              <div class="version-item">
                <div class="version-marker published"></div>
                <div class="version-content">
                  <div class="version-header">
                    <h4>v2.0.5</h4>
                    <span class="version-status published">已发布</span>
                    <span class="version-date">2024-03-01</span>
                  </div>
                  <div class="version-description">
                    优化了产品页面的翻译质量，统一了术语使用
                  </div>
                  <div class="version-stats">
                    <span class="stat-item">233 条目</span>
                    <span class="stat-item">+5 新增</span>
                    <span class="stat-item">-1 删除</span>
                    <span class="stat-item">~15 修改</span>
                  </div>
                  <div class="version-actions">
                    <button class="btn btn-sm btn-secondary" onclick="viewVersion('v2.0.5')">查看详情</button>
                    <button class="btn btn-sm btn-success" onclick="downloadVersion('v2.0.5')">下载</button>
                    <button class="btn btn-sm btn-warning" onclick="rollbackToVersion('v2.0.5')">回滚</button>
                  </div>
                </div>
              </div>

              <div class="version-item">
                <div class="version-marker draft"></div>
                <div class="version-content">
                  <div class="version-header">
                    <h4>v2.0.0</h4>
                    <span class="version-status archived">已归档</span>
                    <span class="version-date">2024-02-15</span>
                  </div>
                  <div class="version-description">
                    重大版本更新，重构了整个翻译架构
                  </div>
                  <div class="version-stats">
                    <span class="stat-item">229 条目</span>
                    <span class="stat-item">+89 新增</span>
                    <span class="stat-item">-12 删除</span>
                    <span class="stat-item">~156 修改</span>
                  </div>
                  <div class="version-actions">
                    <button class="btn btn-sm btn-secondary" onclick="viewVersion('v2.0.0')">查看详情</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 导入语言包模态框 -->
  <div class="modal-overlay" id="importModal">
    <div class="modal">
      <div class="modal-header">
        <h3>导入语言包</h3>
        <button class="modal-close" onclick="closeImportModal()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="upload-area" id="uploadArea">
          <div class="upload-icon">
            <i class="fas fa-cloud-upload-alt"></i>
          </div>
          <div class="upload-text">
            <p>拖拽文件到此处或点击选择文件</p>
            <p class="upload-hint">支持 JSON, CSV, XLSX 格式</p>
          </div>
          <input type="file" id="fileInput" accept=".json,.csv,.xlsx" style="display: none;">
        </div>
        <div class="import-options">
          <div class="form-group">
            <label class="form-label">目标语言</label>
            <select class="form-control" name="targetLanguage">
              <option value="en-US">英语 (en-US)</option>
              <option value="ja-JP">日语 (ja-JP)</option>
              <option value="ko-KR">韩语 (ko-KR)</option>
              <option value="fr-FR">法语 (fr-FR)</option>
            </select>
          </div>
          <div class="form-group">
            <label class="form-label">导入模式</label>
            <select class="form-control" name="importMode">
              <option value="merge">合并模式（保留现有翻译）</option>
              <option value="overwrite">覆盖模式（替换现有翻译）</option>
              <option value="new">仅导入新条目</option>
            </select>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" onclick="closeImportModal()">取消</button>
        <button class="btn btn-primary" onclick="startImport()">开始导入</button>
      </div>
    </div>
  </div>

  <script src="assets/js/common.js"></script>
  <script>
    // 加载组件
    document.addEventListener('DOMContentLoaded', () => {
      fetch('components/sidebar.html')
        .then(response => response.text())
        .then(html => {
          document.getElementById('sidebar-container').innerHTML = html;
        });

      fetch('components/header.html')
        .then(response => response.text())
        .then(html => {
          document.getElementById('header-container').innerHTML = html;
          updateBreadcrumb();
        });

      // 绑定上传区域事件
      bindUploadEvents();
    });

    function updateBreadcrumb() {
      const breadcrumb = document.querySelector('.breadcrumb-current');
      if (breadcrumb) {
        breadcrumb.textContent = '语言包管理';
      }
    }

    // 语言包操作
    function createLanguagePack() {
      console.log('创建新语言包');
    }

    function importLanguagePack() {
      document.getElementById('importModal').style.display = 'flex';
    }

    function closeImportModal() {
      document.getElementById('importModal').style.display = 'none';
    }

    function viewPackDetails(languageCode) {
      console.log('查看语言包详情:', languageCode);
    }

    function editPack(languageCode) {
      window.location.href = `translation-workbench.html?lang=${languageCode}`;
    }

    function downloadPack(languageCode) {
      Utils.showNotification(`正在下载 ${languageCode} 语言包...`, 'info');
    }

    function publishPack(languageCode) {
      Utils.confirm('确定要发布这个语言包吗？', () => {
        Utils.showNotification(`${languageCode} 语言包已发布`, 'success');
      });
    }

    function deletePack(languageCode) {
      Utils.confirm('确定要删除这个语言包吗？此操作不可恢复。', () => {
        Utils.showNotification(`${languageCode} 语言包已删除`, 'success');
      });
    }

    // 版本操作
    function viewVersion(version) {
      console.log('查看版本:', version);
    }

    function downloadVersion(version) {
      Utils.showNotification(`正在下载版本 ${version}...`, 'info');
    }

    function rollbackToVersion(version) {
      Utils.confirm(`确定要回滚到版本 ${version} 吗？`, () => {
        Utils.showNotification(`已回滚到版本 ${version}`, 'success');
      });
    }

    // 上传功能
    function bindUploadEvents() {
      const uploadArea = document.getElementById('uploadArea');
      const fileInput = document.getElementById('fileInput');

      uploadArea.addEventListener('click', () => {
        fileInput.click();
      });

      uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
      });

      uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
      });

      uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
          handleFileUpload(files[0]);
        }
      });

      fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
          handleFileUpload(e.target.files[0]);
        }
      });
    }

    function handleFileUpload(file) {
      console.log('上传文件:', file.name);
      Utils.showNotification(`文件 ${file.name} 已选择`, 'info');
    }

    function startImport() {
      Utils.showNotification('开始导入语言包...', 'info');
      closeImportModal();
    }

    function exportAllPacks() {
      Utils.showNotification('正在导出所有语言包...', 'info');
    }
  </script>
</body>
</html>
