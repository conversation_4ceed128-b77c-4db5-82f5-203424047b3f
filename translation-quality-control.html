<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>翻译质量控制</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/common.css">
</head>
<body data-user-role="project">
  <div class="layout">
    <!-- 侧边栏 -->
    <div id="sidebar-container"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 头部 -->
      <div id="header-container"></div>

      <!-- 内容区域 -->
      <main class="content">
        <!-- 页面标题 -->
        <div class="page-header">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="page-title">
                <i class="fas fa-check-circle"></i>
                翻译质量控制
              </h1>
              <p class="page-description">审核翻译质量，确保多语言内容的准确性</p>
            </div>
            <div class="header-actions">
              <button class="btn btn-secondary" onclick="runQualityCheck()">
                <i class="fas fa-search"></i>
                运行质量检查
              </button>
              <button class="btn btn-primary" onclick="generateQualityReport()">
                <i class="fas fa-file-alt"></i>
                生成质量报告
              </button>
            </div>
          </div>
        </div>

        <!-- 质量概览 -->
        <div class="stats-grid mb-4">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-clipboard-check text-success"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">89.5%</div>
              <div class="stat-label">整体质量评分</div>
              <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                +2.3% 本周
              </div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-exclamation-triangle text-warning"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">23</div>
              <div class="stat-label">待审核条目</div>
              <div class="stat-change negative">
                <i class="fas fa-arrow-down"></i>
                -5 今日
              </div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-bug text-danger"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">7</div>
              <div class="stat-label">发现问题</div>
              <div class="stat-change positive">
                <i class="fas fa-arrow-down"></i>
                -3 今日
              </div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-user-check text-info"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">5</div>
              <div class="stat-label">审核员</div>
              <div class="stat-change neutral">
                <i class="fas fa-minus"></i>
                无变化
              </div>
            </div>
          </div>
        </div>

        <!-- 质量检查工具栏 -->
        <div class="quality-toolbar">
          <div class="toolbar-section">
            <label>语言:</label>
            <select class="form-control" id="languageFilter">
              <option value="">全部语言</option>
              <option value="en-US">英语</option>
              <option value="ja-JP">日语</option>
              <option value="ko-KR">韩语</option>
              <option value="fr-FR">法语</option>
            </select>
          </div>
          <div class="toolbar-section">
            <label>状态:</label>
            <select class="form-control" id="statusFilter">
              <option value="">全部状态</option>
              <option value="pending">待审核</option>
              <option value="approved">已批准</option>
              <option value="rejected">已拒绝</option>
              <option value="needs-revision">需要修改</option>
            </select>
          </div>
          <div class="toolbar-section">
            <label>问题类型:</label>
            <select class="form-control" id="issueFilter">
              <option value="">全部问题</option>
              <option value="grammar">语法错误</option>
              <option value="terminology">术语不一致</option>
              <option value="context">上下文错误</option>
              <option value="formatting">格式问题</option>
            </select>
          </div>
          <div class="toolbar-section">
            <button class="btn btn-secondary" onclick="applyFilters()">
              <i class="fas fa-filter"></i>
              应用筛选
            </button>
          </div>
        </div>

        <!-- 质量检查结果 -->
        <div class="quality-results">
          <!-- 待审核列表 -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">待审核翻译</h3>
              <div class="card-actions">
                <button class="btn btn-sm btn-secondary" onclick="selectAll()">
                  <i class="fas fa-check-square"></i>
                  全选
                </button>
                <button class="btn btn-sm btn-success" onclick="batchApprove()">
                  <i class="fas fa-check"></i>
                  批量批准
                </button>
                <button class="btn btn-sm btn-danger" onclick="batchReject()">
                  <i class="fas fa-times"></i>
                  批量拒绝
                </button>
              </div>
            </div>
            <div class="card-body">
              <div class="review-list">
                <!-- 审核项目 1 -->
                <div class="review-item">
                  <div class="review-header">
                    <input type="checkbox" class="review-checkbox">
                    <div class="review-meta">
                      <span class="review-key">product_description</span>
                      <span class="review-language">🇯🇵 日语</span>
                      <span class="review-status pending">待审核</span>
                    </div>
                    <div class="review-actions">
                      <button class="btn btn-sm btn-secondary" onclick="viewDetails(1)">
                        <i class="fas fa-eye"></i>
                      </button>
                    </div>
                  </div>
                  <div class="review-content">
                    <div class="source-text">
                      <label>源文本:</label>
                      <p>这是一款高品质的智能手机，具有出色的摄影功能和长久的电池续航。</p>
                    </div>
                    <div class="target-text">
                      <label>翻译文本:</label>
                      <p>これは高品質のスマートフォンで、優れた撮影機能と長時間のバッテリー持続時間を備えています。</p>
                    </div>
                    <div class="quality-issues">
                      <div class="issue-tag grammar">语法问题</div>
                      <div class="issue-description">建议使用更自然的日语表达方式</div>
                    </div>
                  </div>
                  <div class="review-footer">
                    <div class="reviewer-info">
                      <span>翻译者: 田中太郎</span>
                      <span>提交时间: 2024-03-15 14:30</span>
                    </div>
                    <div class="review-buttons">
                      <button class="btn btn-sm btn-success" onclick="approveTranslation(1)">
                        <i class="fas fa-check"></i>
                        批准
                      </button>
                      <button class="btn btn-sm btn-warning" onclick="requestRevision(1)">
                        <i class="fas fa-edit"></i>
                        需要修改
                      </button>
                      <button class="btn btn-sm btn-danger" onclick="rejectTranslation(1)">
                        <i class="fas fa-times"></i>
                        拒绝
                      </button>
                    </div>
                  </div>
                </div>

                <!-- 审核项目 2 -->
                <div class="review-item">
                  <div class="review-header">
                    <input type="checkbox" class="review-checkbox">
                    <div class="review-meta">
                      <span class="review-key">checkout_success</span>
                      <span class="review-language">🇰🇷 韩语</span>
                      <span class="review-status pending">待审核</span>
                    </div>
                    <div class="review-actions">
                      <button class="btn btn-sm btn-secondary" onclick="viewDetails(2)">
                        <i class="fas fa-eye"></i>
                      </button>
                    </div>
                  </div>
                  <div class="review-content">
                    <div class="source-text">
                      <label>源文本:</label>
                      <p>订单提交成功！您将收到确认邮件。</p>
                    </div>
                    <div class="target-text">
                      <label>翻译文本:</label>
                      <p>주문이 성공적으로 제출되었습니다! 확인 이메일을 받으실 것입니다.</p>
                    </div>
                    <div class="quality-score">
                      <div class="score-indicator">
                        <span class="score-label">质量评分:</span>
                        <span class="score-value good">92%</span>
                      </div>
                    </div>
                  </div>
                  <div class="review-footer">
                    <div class="reviewer-info">
                      <span>翻译者: 김민수</span>
                      <span>提交时间: 2024-03-15 16:45</span>
                    </div>
                    <div class="review-buttons">
                      <button class="btn btn-sm btn-success" onclick="approveTranslation(2)">
                        <i class="fas fa-check"></i>
                        批准
                      </button>
                      <button class="btn btn-sm btn-warning" onclick="requestRevision(2)">
                        <i class="fas fa-edit"></i>
                        需要修改
                      </button>
                      <button class="btn btn-sm btn-danger" onclick="rejectTranslation(2)">
                        <i class="fas fa-times"></i>
                        拒绝
                      </button>
                    </div>
                  </div>
                </div>

                <!-- 审核项目 3 -->
                <div class="review-item">
                  <div class="review-header">
                    <input type="checkbox" class="review-checkbox">
                    <div class="review-meta">
                      <span class="review-key">error_message</span>
                      <span class="review-language">🇫🇷 法语</span>
                      <span class="review-status needs-revision">需要修改</span>
                    </div>
                    <div class="review-actions">
                      <button class="btn btn-sm btn-secondary" onclick="viewDetails(3)">
                        <i class="fas fa-eye"></i>
                      </button>
                    </div>
                  </div>
                  <div class="review-content">
                    <div class="source-text">
                      <label>源文本:</label>
                      <p>网络连接失败，请检查您的网络设置。</p>
                    </div>
                    <div class="target-text">
                      <label>翻译文本:</label>
                      <p>La connexion réseau a échoué, veuillez vérifier vos paramètres réseau.</p>
                    </div>
                    <div class="quality-issues">
                      <div class="issue-tag terminology">术语不一致</div>
                      <div class="issue-tag context">上下文问题</div>
                      <div class="issue-description">建议使用更用户友好的错误提示语言</div>
                    </div>
                    <div class="revision-history">
                      <div class="revision-item">
                        <span class="revision-author">审核员: Marie Dubois</span>
                        <span class="revision-time">2024-03-15 10:20</span>
                        <p class="revision-comment">请使用更简洁的表达方式，避免过于技术性的词汇。</p>
                      </div>
                    </div>
                  </div>
                  <div class="review-footer">
                    <div class="reviewer-info">
                      <span>翻译者: Pierre Martin</span>
                      <span>提交时间: 2024-03-14 09:15</span>
                    </div>
                    <div class="review-buttons">
                      <button class="btn btn-sm btn-success" onclick="approveTranslation(3)">
                        <i class="fas fa-check"></i>
                        批准
                      </button>
                      <button class="btn btn-sm btn-warning" onclick="requestRevision(3)">
                        <i class="fas fa-edit"></i>
                        继续修改
                      </button>
                      <button class="btn btn-sm btn-danger" onclick="rejectTranslation(3)">
                        <i class="fas fa-times"></i>
                        拒绝
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 质量分析 -->
          <div class="card mt-4">
            <div class="card-header">
              <h3 class="card-title">质量分析报告</h3>
              <div class="card-actions">
                <select class="form-control form-control-sm">
                  <option>最近7天</option>
                  <option>最近30天</option>
                  <option>最近90天</option>
                </select>
              </div>
            </div>
            <div class="card-body">
              <div class="quality-metrics">
                <div class="metric-item">
                  <div class="metric-header">
                    <h4>语言质量分布</h4>
                  </div>
                  <div class="metric-content">
                    <div class="language-quality-item">
                      <span class="language-name">🇺🇸 英语</span>
                      <div class="quality-bar">
                        <div class="quality-fill excellent" style="width: 95%"></div>
                      </div>
                      <span class="quality-score">95%</span>
                    </div>
                    <div class="language-quality-item">
                      <span class="language-name">🇯🇵 日语</span>
                      <div class="quality-bar">
                        <div class="quality-fill good" style="width: 87%"></div>
                      </div>
                      <span class="quality-score">87%</span>
                    </div>
                    <div class="language-quality-item">
                      <span class="language-name">🇰🇷 韩语</span>
                      <div class="quality-bar">
                        <div class="quality-fill good" style="width: 82%"></div>
                      </div>
                      <span class="quality-score">82%</span>
                    </div>
                    <div class="language-quality-item">
                      <span class="language-name">🇫🇷 法语</span>
                      <div class="quality-bar">
                        <div class="quality-fill average" style="width: 74%"></div>
                      </div>
                      <span class="quality-score">74%</span>
                    </div>
                  </div>
                </div>

                <div class="metric-item">
                  <div class="metric-header">
                    <h4>常见问题类型</h4>
                  </div>
                  <div class="metric-content">
                    <div class="issue-stats">
                      <div class="issue-stat-item">
                        <div class="issue-type">语法错误</div>
                        <div class="issue-count">15</div>
                        <div class="issue-percentage">35%</div>
                      </div>
                      <div class="issue-stat-item">
                        <div class="issue-type">术语不一致</div>
                        <div class="issue-count">12</div>
                        <div class="issue-percentage">28%</div>
                      </div>
                      <div class="issue-stat-item">
                        <div class="issue-type">上下文错误</div>
                        <div class="issue-count">8</div>
                        <div class="issue-percentage">19%</div>
                      </div>
                      <div class="issue-stat-item">
                        <div class="issue-type">格式问题</div>
                        <div class="issue-count">7</div>
                        <div class="issue-percentage">16%</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 审核详情模态框 -->
  <div class="modal-overlay" id="reviewModal">
    <div class="modal modal-lg">
      <div class="modal-header">
        <h3>翻译审核详情</h3>
        <button class="modal-close" onclick="closeReviewModal()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="review-detail">
          <div class="detail-section">
            <h4>基本信息</h4>
            <div class="detail-grid">
              <div class="detail-item">
                <label>翻译键:</label>
                <span>product_description</span>
              </div>
              <div class="detail-item">
                <label>目标语言:</label>
                <span>🇯🇵 日语</span>
              </div>
              <div class="detail-item">
                <label>翻译者:</label>
                <span>田中太郎</span>
              </div>
              <div class="detail-item">
                <label>提交时间:</label>
                <span>2024-03-15 14:30</span>
              </div>
            </div>
          </div>
          
          <div class="detail-section">
            <h4>翻译内容</h4>
            <div class="translation-comparison">
              <div class="comparison-item">
                <label>源文本 (中文):</label>
                <div class="text-content">这是一款高品质的智能手机，具有出色的摄影功能和长久的电池续航。</div>
              </div>
              <div class="comparison-item">
                <label>翻译文本 (日语):</label>
                <div class="text-content">これは高品質のスマートフォンで、優れた撮影機能と長時間のバッテリー持続時間を備えています。</div>
              </div>
            </div>
          </div>

          <div class="detail-section">
            <h4>质量评估</h4>
            <div class="quality-assessment">
              <div class="assessment-item">
                <label>AI质量评分:</label>
                <span class="score good">85%</span>
              </div>
              <div class="assessment-item">
                <label>术语一致性:</label>
                <span class="score excellent">95%</span>
              </div>
              <div class="assessment-item">
                <label>语法正确性:</label>
                <span class="score average">78%</span>
              </div>
            </div>
          </div>

          <div class="detail-section">
            <h4>审核意见</h4>
            <textarea class="form-control" rows="4" placeholder="请输入审核意见..."></textarea>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" onclick="closeReviewModal()">关闭</button>
        <button class="btn btn-warning" onclick="requestRevisionWithComment()">需要修改</button>
        <button class="btn btn-danger" onclick="rejectWithComment()">拒绝</button>
        <button class="btn btn-success" onclick="approveWithComment()">批准</button>
      </div>
    </div>
  </div>

  <script src="assets/js/common.js"></script>
  <script>
    // 加载组件
    document.addEventListener('DOMContentLoaded', () => {
      fetch('components/sidebar.html')
        .then(response => response.text())
        .then(html => {
          document.getElementById('sidebar-container').innerHTML = html;
        });

      fetch('components/header.html')
        .then(response => response.text())
        .then(html => {
          document.getElementById('header-container').innerHTML = html;
          updateBreadcrumb();
        });
    });

    function updateBreadcrumb() {
      const breadcrumb = document.querySelector('.breadcrumb-current');
      if (breadcrumb) {
        breadcrumb.textContent = '翻译质量控制';
      }
    }

    // 质量控制功能
    function runQualityCheck() {
      Utils.showNotification('正在运行质量检查...', 'info');
    }

    function generateQualityReport() {
      Utils.showNotification('正在生成质量报告...', 'info');
    }

    function applyFilters() {
      const language = document.getElementById('languageFilter').value;
      const status = document.getElementById('statusFilter').value;
      const issue = document.getElementById('issueFilter').value;
      
      console.log('应用筛选:', { language, status, issue });
      Utils.showNotification('筛选已应用', 'success');
    }

    // 审核操作
    function viewDetails(id) {
      document.getElementById('reviewModal').style.display = 'flex';
    }

    function closeReviewModal() {
      document.getElementById('reviewModal').style.display = 'none';
    }

    function approveTranslation(id) {
      Utils.showNotification(`翻译 ${id} 已批准`, 'success');
    }

    function requestRevision(id) {
      Utils.showNotification(`翻译 ${id} 需要修改`, 'warning');
    }

    function rejectTranslation(id) {
      Utils.confirm('确定要拒绝这个翻译吗？', () => {
        Utils.showNotification(`翻译 ${id} 已拒绝`, 'error');
      });
    }

    // 批量操作
    function selectAll() {
      const checkboxes = document.querySelectorAll('.review-checkbox');
      checkboxes.forEach(cb => cb.checked = true);
    }

    function batchApprove() {
      const selected = document.querySelectorAll('.review-checkbox:checked');
      if (selected.length > 0) {
        Utils.confirm(`确定要批准 ${selected.length} 个翻译吗？`, () => {
          Utils.showNotification(`已批准 ${selected.length} 个翻译`, 'success');
        });
      }
    }

    function batchReject() {
      const selected = document.querySelectorAll('.review-checkbox:checked');
      if (selected.length > 0) {
        Utils.confirm(`确定要拒绝 ${selected.length} 个翻译吗？`, () => {
          Utils.showNotification(`已拒绝 ${selected.length} 个翻译`, 'error');
        });
      }
    }

    // 模态框操作
    function approveWithComment() {
      Utils.showNotification('翻译已批准', 'success');
      closeReviewModal();
    }

    function requestRevisionWithComment() {
      Utils.showNotification('已要求修改', 'warning');
      closeReviewModal();
    }

    function rejectWithComment() {
      Utils.showNotification('翻译已拒绝', 'error');
      closeReviewModal();
    }
  </script>
</body>
</html>
